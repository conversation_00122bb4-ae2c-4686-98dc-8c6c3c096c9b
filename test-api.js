// Test Plant.id API key
const apiKey = "KmHZnfuDmX3iWAU73hGrySRfFd0mrdwJLQ6UrQlOdX99hMNan4";

// Simple 1x1 pixel image in base64
const testImage = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==";

const requestBody = {
  images: [testImage],
  modifiers: ["crops_fast"],
  plant_details: ["common_names"]
};

console.log("Testing Plant.id API key...");
console.log("API Key:", apiKey.substring(0, 10) + "...");

fetch("https://plant.id/api/v3/identification", {
  method: "POST",
  headers: {
    "Content-Type": "application/json",
    "Api-Key": apiKey,
  },
  body: JSON.stringify(requestBody),
})
.then(response => {
  console.log("Response status:", response.status);
  console.log("Response headers:", Object.fromEntries(response.headers.entries()));
  return response.text();
})
.then(text => {
  console.log("Response body:", text);
  try {
    const json = JSON.parse(text);
    console.log("Parsed JSON:", json);
  } catch (e) {
    console.log("Failed to parse as JSON");
  }
})
.catch(error => {
  console.error("Error:", error);
});
