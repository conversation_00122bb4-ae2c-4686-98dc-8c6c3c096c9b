import { json, type ActionFunctionArgs } from "@remix-run/node";

/**
 * Plant.id API v3 Integration
 *
 * This module integrates with the Plant.id API v3 for AI-powered plant identification.
 *
 * API Documentation: https://documenter.getpostman.com/view/24599534/2s93z5A4v2
 *
 * Key Features:
 * - Plant species identification from images
 * - Similar image matching
 * - Health assessment (auto mode)
 * - Geographic context support
 * - Comprehensive plant details (taxonomy, common names, descriptions)
 *
 * Setup:
 * 1. Sign up at https://plant.id/
 * 2. Get your API key from the dashboard
 * 3. Add PLANT_ID_API_KEY=your_key_here to your .env file
 * 4. Restart your server
 *
 * API Costs:
 * - Basic identification: 1 credit per request
 * - With health assessment: 2 credits per request
 * - Free tier includes limited credits
 */

// Plant.id API v3 response interfaces (based on official documentation)
interface PlantIdSuggestion {
  id: number;
  name: string;
  probability: number;
  confirmed: boolean;
  similar_images?: Array<{
    id: string;
    url: string;
    license_name?: string;
    license_url?: string;
    citation?: string;
    similarity: number;
    url_small: string;
  }>;
  details: {
    common_names?: string[];
    url?: string;
    description?: {
      value: string;
      citation?: string;
    };
    taxonomy?: {
      class?: string;
      family?: string;
      genus?: string;
      kingdom?: string;
      order?: string;
      phylum?: string;
    };
    synonyms?: string[];
  };
}

interface PlantIdResponse {
  access_token: string;
  result: {
    is_plant: {
      binary: boolean;
      probability: number;
    };
    classification: {
      suggestions: PlantIdSuggestion[];
    };
  };
  // Legacy fields for backward compatibility
  id?: number;
  suggestions?: PlantIdSuggestion[];
  is_plant_probability?: number;
}

// Our enhanced result interface with care information
interface PlantIdentificationResult {
  id: string;
  name: string;
  scientificName: string;
  commonNames: string[];
  confidence: number;
  description?: string;
  image?: string;
  similarImages: Array<{
    url: string;
    similarity: number;
  }>;
  taxonomy?: {
    family?: string;
    genus?: string;
    class?: string;
  };
  isPlantProbability: number;
  topMatches: Array<{
    name: string;
    scientificName: string;
    confidence: number;
    image?: string;
  }>;
  careGuide: {
    light: string;
    water: string;
    humidity: string;
    temperature: string;
    soil: string;
    fertilizer: string;
    repotting: string;
    commonIssues: string[];
    tips: string[];
  };
}

// Plant care database - comprehensive care information for common houseplants
const PLANT_CARE_DATABASE: Record<string, any> = {
  // Philodendrons
  "philodendron": {
    light: "Bright, indirect light. Avoid direct sunlight which can scorch leaves.",
    water: "Water when top 1-2 inches of soil are dry. Usually every 1-2 weeks.",
    humidity: "Prefers 50-60% humidity. Use a humidifier or pebble tray.",
    temperature: "65-78°F (18-26°C). Avoid cold drafts and sudden temperature changes.",
    soil: "Well-draining potting mix with peat moss or coco coir.",
    fertilizer: "Monthly during growing season (spring/summer) with balanced liquid fertilizer.",
    repotting: "Every 2-3 years or when rootbound. Spring is best time.",
    commonIssues: ["Yellow leaves (overwatering)", "Brown leaf tips (low humidity)", "Leggy growth (insufficient light)"],
    tips: ["Wipe leaves regularly", "Provide climbing support for vining varieties", "Propagate easily in water"]
  },

  // Monsteras
  "monstera": {
    light: "Bright, indirect light. Can tolerate some direct morning sun.",
    water: "Water when top inch of soil is dry. Every 1-2 weeks typically.",
    humidity: "50-60% humidity ideal. Mist regularly or use humidifier.",
    temperature: "65-80°F (18-27°C). Warm, stable temperatures preferred.",
    soil: "Well-draining, chunky mix with bark, perlite, and peat.",
    fertilizer: "Monthly in spring/summer with balanced fertilizer.",
    repotting: "Every 2-3 years. Use a pot only 1-2 inches larger.",
    commonIssues: ["No fenestrations (insufficient light)", "Root rot (overwatering)", "Pest issues (spider mites, scale)"],
    tips: ["Provide moss pole for climbing", "Fenestrations develop with maturity", "Aerial roots are normal"]
  },

  // Pothos
  "pothos": {
    light: "Low to bright indirect light. Very adaptable to different conditions.",
    water: "Water when soil is mostly dry. Every 1-2 weeks.",
    humidity: "Average home humidity (40-50%) is fine.",
    temperature: "65-85°F (18-29°C). Very tolerant of temperature fluctuations.",
    soil: "Standard potting mix with good drainage.",
    fertilizer: "Monthly during growing season with diluted liquid fertilizer.",
    repotting: "Every 2-3 years or when severely rootbound.",
    commonIssues: ["Leggy growth (low light)", "Yellow leaves (overwatering)", "Brown spots (overwatering or disease)"],
    tips: ["Extremely easy to propagate", "Trim regularly to maintain shape", "Can grow in water indefinitely"]
  },

  // Snake Plants
  "sansevieria": {
    light: "Low to bright indirect light. Very tolerant of low light.",
    water: "Water sparingly. Every 2-6 weeks depending on season and conditions.",
    humidity: "Low humidity preferred. Average home humidity is perfect.",
    temperature: "60-80°F (15-27°C). Can tolerate cooler temperatures.",
    soil: "Well-draining cactus/succulent mix or regular potting soil with perlite.",
    fertilizer: "2-3 times per year during growing season with diluted fertilizer.",
    repotting: "Every 3-5 years. They prefer to be slightly rootbound.",
    commonIssues: ["Root rot (overwatering)", "Soft/mushy leaves (overwatering)", "Slow growth (normal)"],
    tips: ["Perfect for beginners", "Can survive neglect", "Propagate by leaf cuttings or division"]
  },

  // Rubber Trees
  "ficus": {
    light: "Bright, indirect light. Can handle some direct morning sun.",
    water: "Water when top inch of soil is dry. Every 1-2 weeks.",
    humidity: "40-50% humidity. Mist leaves occasionally.",
    temperature: "65-80°F (18-27°C). Avoid cold drafts.",
    soil: "Well-draining potting mix with some perlite.",
    fertilizer: "Monthly during spring/summer with balanced liquid fertilizer.",
    repotting: "Every 2-3 years or when rootbound.",
    commonIssues: ["Leaf drop (stress, overwatering, or underwatering)", "Brown leaf edges (low humidity)", "Sticky sap (normal)"],
    tips: ["Wipe leaves regularly for shine", "Prune to control size", "Wear gloves when handling (sap can irritate)"]
  },

  // ZZ Plants
  "zamioculcas": {
    light: "Low to bright indirect light. Very adaptable.",
    water: "Water when soil is completely dry. Every 2-4 weeks.",
    humidity: "Low humidity preferred. Average home humidity is fine.",
    temperature: "65-79°F (18-26°C). Can tolerate cooler temperatures.",
    soil: "Well-draining potting mix or cactus mix.",
    fertilizer: "2-3 times per year during growing season.",
    repotting: "Every 2-3 years. They prefer to be slightly rootbound.",
    commonIssues: ["Yellow leaves (overwatering)", "Soft stems (overwatering)", "Slow growth (normal)"],
    tips: ["Extremely drought tolerant", "Perfect for low-light areas", "All parts are toxic if ingested"]
  }
};

// Generate care guide based on plant identification
function generateCareGuide(scientificName: string, commonNames: string[]): any {
  const plantName = scientificName.toLowerCase();
  const allNames = [scientificName, ...commonNames].map(name => name.toLowerCase());

  // Try to match against our care database
  for (const [key, care] of Object.entries(PLANT_CARE_DATABASE)) {
    if (plantName.includes(key) || allNames.some(name => name.includes(key))) {
      return care;
    }
  }

  // Default care guide for unknown plants
  return {
    light: "Bright, indirect light. Most houseplants prefer this condition.",
    water: "Water when top inch of soil feels dry. Usually every 1-2 weeks.",
    humidity: "Average home humidity (40-50%) is usually sufficient.",
    temperature: "65-75°F (18-24°C). Avoid cold drafts and sudden temperature changes.",
    soil: "Well-draining potting mix suitable for houseplants.",
    fertilizer: "Monthly during spring and summer with balanced liquid fertilizer.",
    repotting: "Every 2-3 years or when the plant becomes rootbound.",
    commonIssues: ["Yellow leaves (often overwatering)", "Brown leaf tips (low humidity or water quality)", "Slow growth (normal in winter)"],
    tips: ["Research specific care for your plant species", "Observe your plant's response to care", "Start with less water rather than more"]
  };
}

// Call Plant.id API for plant identification
async function identifyPlantWithPlantId(imageBase64: string): Promise<PlantIdResponse> {
  const apiKey = process.env.PLANT_ID_API_KEY;

  // Check if API key is configured
  if (!apiKey || apiKey === "your_plant_id_api_key_here") {
    throw new Error("Plant.id API key not configured. Please add your API key to the .env file.");
  }

  const requestBody = {
    images: [imageBase64],
    similar_images: true,
    // Health assessment: "auto" includes health only if plant appears diseased
    health: "auto",
    // Custom ID for tracking this identification
    custom_id: Math.floor(Math.random() * 1000000)
    // Note: Removed plant_details parameter as it's not valid in API v3
    // Note: Removed datetime parameter as it was causing format issues
    // Geographic coordinates can improve identification accuracy
    // Uncomment and set if user location is available:
    // latitude: 37.7749,
    // longitude: -122.4194
  };

  console.log("Sending request to Plant.id API...");
  console.log("Request body:", JSON.stringify(requestBody, null, 2));

  try {
    const response = await fetch("https://api.plant.id/v3/identification", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Api-Key": apiKey,
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Plant.id API error:", {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
        body: errorText
      });

      // Handle specific API errors
      if (response.status === 401) {
        throw new Error("Invalid Plant.id API key. Please check your API key in the .env file.");
      }

      if (response.status === 402) {
        throw new Error("Plant.id API quota exceeded. Please check your account credits.");
      }

      if (response.status === 400) {
        throw new Error("Invalid request format. Please try with a different image.");
      }

      throw new Error(`Plant.id API error: ${response.status} ${errorText}`);
    }

    const result: PlantIdResponse = await response.json();
    console.log("✅ Plant.id API response received:", {
      access_token: result.access_token,
      is_plant_probability: result.result?.is_plant?.probability,
      suggestions_count: result.result?.classification?.suggestions?.length || 0
    });
    return result;

  } catch (error) {
    console.error("Plant.id API request failed:", error);
    throw error; // Re-throw the error instead of falling back to mock
  }
}

// Process Plant.id response into our enhanced format with care guide
function processPlantIdResponse(plantIdResponse: PlantIdResponse): PlantIdentificationResult {
  // Handle both new API v3 format and legacy format
  const suggestions = plantIdResponse.result?.classification?.suggestions || plantIdResponse.suggestions || [];
  const isPlantProbability = plantIdResponse.result?.is_plant?.probability || plantIdResponse.is_plant_probability || 0;
  const responseId = plantIdResponse.access_token || plantIdResponse.id?.toString() || 'unknown';

  if (suggestions.length === 0) {
    throw new Error("No plant suggestions found");
  }

  const topSuggestion = suggestions[0];
  const scientificName = topSuggestion.name;
  const commonNames = topSuggestion.details?.common_names || [];
  const plantName = commonNames[0] || scientificName;

  // Generate comprehensive care guide
  const careGuide = generateCareGuide(scientificName, commonNames);

  const topMatches = suggestions.slice(0, 3).map(suggestion => ({
    name: suggestion.details?.common_names?.[0] || suggestion.name,
    scientificName: suggestion.name,
    confidence: suggestion.probability,
    image: suggestion.similar_images?.[0]?.url_small
  }));

  // Get the best available image
  const plantImage = topSuggestion.similar_images?.[0]?.url || topSuggestion.similar_images?.[0]?.url_small;

  return {
    id: `plant-${responseId}`,
    name: plantName,
    scientificName: scientificName,
    commonNames: commonNames,
    confidence: topSuggestion.probability,
    description: topSuggestion.details?.description?.value || `${plantName} is a beautiful plant that makes an excellent addition to any home or garden.`,
    image: plantImage,
    similarImages: topSuggestion.similar_images?.slice(0, 5).map(img => ({
      url: img.url_small || img.url,
      similarity: img.similarity || 0.8
    })) || [],
    taxonomy: {
      family: topSuggestion.details?.taxonomy?.family,
      genus: topSuggestion.details?.taxonomy?.genus,
      class: topSuggestion.details?.taxonomy?.class
    },
    isPlantProbability: isPlantProbability,
    topMatches,
    careGuide
  };
}

export async function action({ request }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }

  try {
    const formData = await request.formData();
    const imageData = formData.get("image") as string;

    if (!imageData) {
      return json({ error: "No image provided" }, { status: 400 });
    }

    // Validate base64 image format
    if (!imageData.startsWith("data:image/")) {
      return json({ error: "Invalid image format" }, { status: 400 });
    }

    // Extract base64 data from data URL
    const base64Data = imageData.split(",")[1];
    if (!base64Data) {
      return json({ error: "Invalid image format" }, { status: 400 });
    }

    console.log("Starting Plant.id identification...");
    console.log("API Key configured:", !!process.env.PLANT_ID_API_KEY);
    console.log("Image size:", base64Data.length, "characters");

    // Call Plant.id API
    const plantIdResponse = await identifyPlantWithPlantId(base64Data);

    // Handle both new API v3 format and legacy format
    const isPlantProbability = plantIdResponse.result?.is_plant?.probability || plantIdResponse.is_plant_probability || 0;
    const suggestions = plantIdResponse.result?.classification?.suggestions || plantIdResponse.suggestions || [];

    console.log("Plant.id response received, is_plant_probability:", isPlantProbability);
    console.log("Number of suggestions:", suggestions.length);

    // Check if it's likely a plant
    if (isPlantProbability < 0.5) {
      return json({
        success: false,
        error: "This doesn't appear to be a plant. Please try uploading a clearer photo of a plant.",
        isPlantProbability: isPlantProbability
      });
    }

    // Process the response
    const result = processPlantIdResponse(plantIdResponse);

    console.log("Plant identification completed:", result.name, `(${Math.round(result.confidence * 100)}%)`);

    return json({
      success: true,
      result,
      lowConfidence: result.confidence < 0.7
    });

  } catch (error) {
    console.error("Plant identification error details:", {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      apiKeyExists: !!process.env.PLANT_ID_API_KEY
    });

    if (error instanceof Error && error.message.includes("Plant.id API key")) {
      return json(
        { error: "Plant identification service is not configured. Please check API key." },
        { status: 500 }
      );
    }

    if (error instanceof Error && error.message.includes("Plant.id API error")) {
      return json(
        { error: `Plant.id service error: ${error.message}` },
        { status: 500 }
      );
    }

    return json(
      { error: "Failed to identify plant. Please try again with a clearer photo." },
      { status: 500 }
    );
  }
}
