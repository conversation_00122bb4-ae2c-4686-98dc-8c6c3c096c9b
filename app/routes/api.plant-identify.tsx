import { json, type ActionFunctionArgs } from "@remix-run/node";

/**
 * Plant.id API v3 Integration
 *
 * This module integrates with the Plant.id API v3 for AI-powered plant identification.
 *
 * API Documentation: https://documenter.getpostman.com/view/24599534/2s93z5A4v2
 *
 * Key Features:
 * - Plant species identification from images
 * - Similar image matching
 * - Health assessment (auto mode)
 * - Geographic context support
 * - Comprehensive plant details (taxonomy, common names, descriptions)
 *
 * Setup:
 * 1. Sign up at https://plant.id/
 * 2. Get your API key from the dashboard
 * 3. Add PLANT_ID_API_KEY=your_key_here to your .env file
 * 4. Restart your server
 *
 * API Costs:
 * - Basic identification: 1 credit per request
 * - With health assessment: 2 credits per request
 * - Free tier includes limited credits
 */

// Plant.id API v3 response interfaces (based on official documentation)
interface PlantIdSuggestion {
  id: number;
  name: string;
  probability: number;
  confirmed: boolean;
  similar_images?: Array<{
    id: string;
    url: string;
    license_name?: string;
    license_url?: string;
    citation?: string;
    similarity: number;
    url_small: string;
  }>;
  details: {
    common_names?: string[];
    url?: string;
    description?: {
      value: string;
      citation?: string;
    };
    taxonomy?: {
      class?: string;
      family?: string;
      genus?: string;
      kingdom?: string;
      order?: string;
      phylum?: string;
    };
    synonyms?: string[];
  };
}

interface PlantIdResponse {
  access_token: string;
  result: {
    is_plant: {
      binary: boolean;
      probability: number;
    };
    classification: {
      suggestions: PlantIdSuggestion[];
    };
  };
  // Legacy fields for backward compatibility
  id?: number;
  suggestions?: PlantIdSuggestion[];
  is_plant_probability?: number;
}

// Our enhanced result interface with care information
interface PlantIdentificationResult {
  id: string;
  name: string;
  scientificName: string;
  commonNames: string[];
  confidence: number;
  description?: string;
  image?: string;
  similarImages: Array<{
    url: string;
    similarity: number;
  }>;
  taxonomy?: {
    family?: string;
    genus?: string;
    class?: string;
  };
  isPlantProbability: number;
  topMatches: Array<{
    name: string;
    scientificName: string;
    confidence: number;
    image?: string;
  }>;
  careGuide: {
    light: string;
    water: string;
    humidity: string;
    temperature: string;
    soil: string;
    fertilizer: string;
    repotting: string;
    commonIssues: string[];
    tips: string[];
    customSoilMix: {
      description: string;
      totalVolume: string;
      bucketMeasurements: Array<{
        material: string;
        buckets: number;
        liters: number;
        purpose: string;
      }>;
      layering: {
        bottom: string[];
        middle: string[];
        top: string[];
      };
      preparation: string[];
      notes: string[];
    };
  };
}

// Plant care database - comprehensive care information for common houseplants
const PLANT_CARE_DATABASE: Record<string, any> = {
  // Philodendrons
  "philodendron": {
    light: "Bright, indirect light. Avoid direct sunlight which can scorch leaves.",
    water: "Water when top 1-2 inches of soil are dry. Usually every 1-2 weeks.",
    humidity: "Prefers 50-60% humidity. Use a humidifier or pebble tray.",
    temperature: "65-78°F (18-26°C). Avoid cold drafts and sudden temperature changes.",
    soil: "Well-draining potting mix with peat moss or coco coir.",
    fertilizer: "Monthly during growing season (spring/summer) with balanced liquid fertilizer.",
    repotting: "Every 2-3 years or when rootbound. Spring is best time.",
    commonIssues: ["Yellow leaves (overwatering)", "Brown leaf tips (low humidity)", "Leggy growth (insufficient light)"],
    tips: ["Wipe leaves regularly", "Provide climbing support for vining varieties", "Propagate easily in water"],
    customSoilMix: {
      description: "Aroid-friendly mix with excellent drainage and organic matter for climbing philodendrons",
      totalVolume: "100 liters (5 buckets of 20L each)",
      bucketMeasurements: [
        { material: "Old soil", buckets: 1.5, liters: 30, purpose: "Base structure and existing nutrients" },
        { material: "Compost", buckets: 1, liters: 20, purpose: "Rich organic matter and slow-release nutrients" },
        { material: "Coconut husk chunks", buckets: 1, liters: 20, purpose: "Chunky drainage and aeration" },
        { material: "Coconut coir", buckets: 1, liters: 20, purpose: "Moisture retention and root support" },
        { material: "Dried leaves", buckets: 0.5, liters: 10, purpose: "Natural mulch and gradual decomposition" }
      ],
      layering: {
        bottom: ["Small sticks", "Fallen coconut fronds big", "Coconut husk chunks"],
        middle: ["Mixed soil blend", "Compost", "Coconut coir"],
        top: ["Dried leaves", "Fallen coconut fronds chopped"]
      },
      preparation: [
        "Mix old soil, compost, coconut husk chunks, and coconut coir thoroughly",
        "Add 2-3 tablespoons liquid microbial fertilizer to the mix",
        "Add 1 tablespoon fish emulsion per 20L bucket",
        "Let mixture rest for 24-48 hours before planting"
      ],
      notes: [
        "This mix provides excellent drainage while retaining moisture",
        "Perfect for epiphytic growth habits of philodendrons",
        "Organic materials will decompose slowly, providing long-term nutrition"
      ]
    }
  },

  // Monsteras
  "monstera": {
    light: "Bright, indirect light. Can tolerate some direct morning sun.",
    water: "Water when top inch of soil is dry. Every 1-2 weeks typically.",
    humidity: "50-60% humidity ideal. Mist regularly or use humidifier.",
    temperature: "65-80°F (18-27°C). Warm, stable temperatures preferred.",
    soil: "Well-draining, chunky mix with bark, perlite, and peat.",
    fertilizer: "Monthly in spring/summer with balanced fertilizer.",
    repotting: "Every 2-3 years. Use a pot only 1-2 inches larger.",
    commonIssues: ["No fenestrations (insufficient light)", "Root rot (overwatering)", "Pest issues (spider mites, scale)"],
    tips: ["Provide moss pole for climbing", "Fenestrations develop with maturity", "Aerial roots are normal"],
    customSoilMix: {
      description: "Chunky, well-draining mix perfect for large Monstera growth and aerial root development",
      totalVolume: "100 liters (5 buckets of 20L each)",
      bucketMeasurements: [
        { material: "Old soil", buckets: 1, liters: 20, purpose: "Base structure and stability" },
        { material: "Compost", buckets: 1.5, liters: 30, purpose: "Rich nutrients for large leaf development" },
        { material: "Coconut husk chunks", buckets: 1.5, liters: 30, purpose: "Excellent drainage and chunky texture" },
        { material: "Coconut coir", buckets: 0.75, liters: 15, purpose: "Moisture retention without waterlogging" },
        { material: "Small sticks", buckets: 0.25, liters: 5, purpose: "Extra drainage and aeration" }
      ],
      layering: {
        bottom: ["Fallen coconut fronds big", "Small sticks", "Coconut husk chunks"],
        middle: ["Mixed soil blend", "Compost", "Coconut husk chunks"],
        top: ["Coconut coir", "Fallen coconut fronds chopped"]
      },
      preparation: [
        "Create chunky mix by combining all materials loosely",
        "Add 3-4 tablespoons liquid microbial fertilizer for root health",
        "Mix in 1.5 tablespoons fish emulsion per 20L bucket",
        "Ensure mix is chunky, not compacted"
      ],
      notes: [
        "Chunky texture supports aerial root attachment",
        "Excellent for large Monstera varieties",
        "Provides stability for climbing growth"
      ]
    }
  },

  // Pothos
  "pothos": {
    light: "Low to bright indirect light. Very adaptable to different conditions.",
    water: "Water when soil is mostly dry. Every 1-2 weeks.",
    humidity: "Average home humidity (40-50%) is fine.",
    temperature: "65-85°F (18-29°C). Very tolerant of temperature fluctuations.",
    soil: "Standard potting mix with good drainage.",
    fertilizer: "Monthly during growing season with diluted liquid fertilizer.",
    repotting: "Every 2-3 years or when severely rootbound.",
    commonIssues: ["Leggy growth (low light)", "Yellow leaves (overwatering)", "Brown spots (overwatering or disease)"],
    tips: ["Extremely easy to propagate", "Trim regularly to maintain shape", "Can grow in water indefinitely"],
    customSoilMix: {
      description: "Balanced, forgiving mix perfect for easy-care trailing Pothos varieties",
      totalVolume: "100 liters (5 buckets of 20L each)",
      bucketMeasurements: [
        { material: "Old soil", buckets: 2, liters: 40, purpose: "Stable base for consistent growth" },
        { material: "Compost", buckets: 1, liters: 20, purpose: "Gentle nutrition for steady growth" },
        { material: "Coconut coir", buckets: 1.5, liters: 30, purpose: "Moisture retention and root support" },
        { material: "Dried leaves", buckets: 0.5, liters: 10, purpose: "Natural mulch and slow decomposition" }
      ],
      layering: {
        bottom: ["Small sticks", "Fallen coconut fronds chopped"],
        middle: ["Mixed soil blend", "Compost", "Coconut coir"],
        top: ["Dried leaves", "Light coconut coir layer"]
      },
      preparation: [
        "Mix all materials evenly for consistent texture",
        "Add 2 tablespoons liquid microbial fertilizer",
        "Add 1 tablespoon fish emulsion per 20L bucket",
        "Ensure good moisture retention without waterlogging"
      ],
      notes: [
        "Forgiving mix that tolerates various watering schedules",
        "Perfect for beginners and busy plant parents",
        "Supports both hanging and climbing growth"
      ]
    }
  },

  // Snake Plants
  "sansevieria": {
    light: "Low to bright indirect light. Very tolerant of low light.",
    water: "Water sparingly. Every 2-6 weeks depending on season and conditions.",
    humidity: "Low humidity preferred. Average home humidity is perfect.",
    temperature: "60-80°F (15-27°C). Can tolerate cooler temperatures.",
    soil: "Well-draining cactus/succulent mix or regular potting soil with perlite.",
    fertilizer: "2-3 times per year during growing season with diluted fertilizer.",
    repotting: "Every 3-5 years. They prefer to be slightly rootbound.",
    commonIssues: ["Root rot (overwatering)", "Soft/mushy leaves (overwatering)", "Slow growth (normal)"],
    tips: ["Perfect for beginners", "Can survive neglect", "Propagate by leaf cuttings or division"],
    customSoilMix: {
      description: "Fast-draining, low-maintenance mix perfect for drought-tolerant Snake Plants",
      totalVolume: "100 liters (5 buckets of 20L each)",
      bucketMeasurements: [
        { material: "Old soil", buckets: 1.5, liters: 30, purpose: "Base structure with existing drainage" },
        { material: "Coconut husk chunks", buckets: 2, liters: 40, purpose: "Excellent drainage and aeration" },
        { material: "Compost", buckets: 0.75, liters: 15, purpose: "Light nutrition without water retention" },
        { material: "Small sticks", buckets: 0.5, liters: 10, purpose: "Extra drainage and air pockets" },
        { material: "Dried leaves", buckets: 0.25, liters: 5, purpose: "Light mulch layer" }
      ],
      layering: {
        bottom: ["Fallen coconut fronds big", "Small sticks", "Coconut husk chunks"],
        middle: ["Mixed soil blend", "Coconut husk chunks", "Light compost"],
        top: ["Dried leaves", "Small coconut husk pieces"]
      },
      preparation: [
        "Create very well-draining mix with chunky materials",
        "Add only 1 tablespoon liquid microbial fertilizer (light feeding)",
        "Add 0.5 tablespoon fish emulsion per 20L bucket (minimal nutrition)",
        "Ensure mix drains quickly when watered"
      ],
      notes: [
        "Extremely well-draining to prevent root rot",
        "Low nutrition mix suits slow-growing nature",
        "Perfect for neglect-tolerant care style"
      ]
    }
  },

  // Rubber Trees
  "ficus": {
    light: "Bright, indirect light. Can handle some direct morning sun.",
    water: "Water when top inch of soil is dry. Every 1-2 weeks.",
    humidity: "40-50% humidity. Mist leaves occasionally.",
    temperature: "65-80°F (18-27°C). Avoid cold drafts.",
    soil: "Well-draining potting mix with some perlite.",
    fertilizer: "Monthly during spring/summer with balanced liquid fertilizer.",
    repotting: "Every 2-3 years or when rootbound.",
    commonIssues: ["Leaf drop (stress, overwatering, or underwatering)", "Brown leaf edges (low humidity)", "Sticky sap (normal)"],
    tips: ["Wipe leaves regularly for shine", "Prune to control size", "Wear gloves when handling (sap can irritate)"]
  },

  // ZZ Plants
  "zamioculcas": {
    light: "Low to bright indirect light. Very adaptable.",
    water: "Water when soil is completely dry. Every 2-4 weeks.",
    humidity: "Low humidity preferred. Average home humidity is fine.",
    temperature: "65-79°F (18-26°C). Can tolerate cooler temperatures.",
    soil: "Well-draining potting mix or cactus mix.",
    fertilizer: "2-3 times per year during growing season.",
    repotting: "Every 2-3 years. They prefer to be slightly rootbound.",
    commonIssues: ["Yellow leaves (overwatering)", "Soft stems (overwatering)", "Slow growth (normal)"],
    tips: ["Extremely drought tolerant", "Perfect for low-light areas", "All parts are toxic if ingested"]
  }
};

// Generate AI-powered plant-specific care guide
async function generateCareGuide(scientificName: string, commonNames: string[], taxonomy: any, description?: string): Promise<any> {
  try {
    // Use AI to generate completely custom care guide for this specific plant
    return await generateAICareGuide(scientificName, commonNames, taxonomy, description);
  } catch (error) {
    console.error("AI care guide generation failed, falling back to template:", error);
    // Fallback to template-based system if AI fails
    return generateTemplateCareGuide(scientificName, commonNames, taxonomy);
  }
}

// AI-powered care guide generation
async function generateAICareGuide(scientificName: string, commonNames: string[], taxonomy: any, description?: string): Promise<any> {
  const plantName = commonNames[0] || scientificName;
  const family = taxonomy?.family || "Unknown";
  const genus = taxonomy?.genus || "Unknown";

  // Create a comprehensive prompt for AI
  const prompt = `You are a professional botanist and plant care expert. Generate a comprehensive care guide for the following plant:

Plant Information:
- Scientific Name: ${scientificName}
- Common Names: ${commonNames.join(", ")}
- Family: ${family}
- Genus: ${genus}
${description ? `- Description: ${description}` : ""}

Please provide detailed, specific care instructions for this exact plant species. Include:

1. LIGHT: Specific light requirements, placement recommendations, and what to avoid
2. WATER: Watering frequency, soil moisture indicators, seasonal adjustments
3. HUMIDITY: Ideal humidity levels and how to achieve them
4. TEMPERATURE: Optimal temperature ranges and what to avoid
5. SOIL: Specific soil requirements and drainage needs
6. FERTILIZER: Feeding schedule, fertilizer types, and application methods
7. REPOTTING: When and how to repot, pot sizing recommendations
8. COMMON_ISSUES: 4-5 specific problems this plant commonly faces
9. TIPS: 4-5 expert tips for optimal care of this specific plant

For the custom soil mix, create a recipe using these available materials for a 100-liter plant bed using 20-liter measuring buckets:
- Old soil
- Compost
- Coconut husk chunks
- Coconut coir
- Small sticks
- Fallen coconut fronds (big and chopped)
- Dried leaves
- Liquid microbial fertilizer
- Fish emulsion

Provide exact bucket measurements, layering instructions (bottom/middle/top), preparation steps, and specific notes for this plant.

Format your response as a JSON object with this exact structure:
{
  "light": "detailed light requirements",
  "water": "detailed watering instructions",
  "humidity": "detailed humidity requirements",
  "temperature": "detailed temperature requirements",
  "soil": "detailed soil requirements",
  "fertilizer": "detailed fertilizer instructions",
  "repotting": "detailed repotting instructions",
  "commonIssues": ["issue1", "issue2", "issue3", "issue4"],
  "tips": ["tip1", "tip2", "tip3", "tip4"],
  "customSoilMix": {
    "description": "description of why this mix works for this plant",
    "totalVolume": "100 liters (5 buckets of 20L each)",
    "bucketMeasurements": [
      {"material": "material name", "buckets": number, "liters": number, "purpose": "why this material"}
    ],
    "layering": {
      "bottom": ["materials for bottom layer"],
      "middle": ["materials for middle layer"],
      "top": ["materials for top layer"]
    },
    "preparation": ["step1", "step2", "step3"],
    "notes": ["note1", "note2", "note3"]
  }
}

Be specific to this exact plant species. Avoid generic advice.`;

  // Call OpenAI API (you'll need to add your API key)
  const response = await callOpenAI(prompt);

  try {
    return JSON.parse(response);
  } catch (parseError) {
    console.error("Failed to parse AI response:", parseError);
    throw new Error("Invalid AI response format");
  }
}

// OpenAI API call function
async function callOpenAI(prompt: string): Promise<string> {
  const apiKey = process.env.OPENAI_API_KEY;

  if (!apiKey) {
    throw new Error("OpenAI API key not configured");
  }

  const response = await fetch("https://api.openai.com/v1/chat/completions", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${apiKey}`,
    },
    body: JSON.stringify({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: "You are a professional botanist and plant care expert. Provide detailed, accurate, and specific plant care advice."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      max_tokens: 2000,
      temperature: 0.3, // Lower temperature for more consistent, factual responses
    }),
  });

  if (!response.ok) {
    const error = await response.text();
    throw new Error(`OpenAI API error: ${response.status} ${error}`);
  }

  const data = await response.json();
  return data.choices[0]?.message?.content || "";
}

// Fallback template-based care guide (existing system)
function generateTemplateCareGuide(scientificName: string, commonNames: string[], taxonomy: any): any {
  const plantName = scientificName.toLowerCase();
  const allNames = [scientificName, ...commonNames].map(name => name.toLowerCase());
  const family = taxonomy?.family?.toLowerCase() || "";
  const genus = taxonomy?.genus?.toLowerCase() || "";

  // First try exact matches from our database for common plants
  for (const [key, care] of Object.entries(PLANT_CARE_DATABASE)) {
    if (plantName.includes(key) || allNames.some(name => name.includes(key))) {
      return care;
    }
  }

  // Generate plant-specific care based on plant family and characteristics
  return generatePlantSpecificCare(scientificName, commonNames, family, genus);
}

// Generate plant-specific care based on botanical characteristics
function generatePlantSpecificCare(scientificName: string, commonNames: string[], family: string, genus: string): any {
  const plantName = commonNames[0] || scientificName;

  // Determine plant characteristics based on family and genus
  const characteristics = analyzePlantCharacteristics(family, genus, scientificName);

  return {
    light: generateLightRequirements(characteristics, plantName),
    water: generateWaterRequirements(characteristics, plantName),
    humidity: generateHumidityRequirements(characteristics, plantName),
    temperature: generateTemperatureRequirements(characteristics, plantName),
    soil: generateSoilRequirements(characteristics, plantName),
    fertilizer: generateFertilizerRequirements(characteristics, plantName),
    repotting: generateRepottingRequirements(characteristics, plantName),
    commonIssues: generateCommonIssues(characteristics, plantName),
    tips: generateCareTips(characteristics, plantName),
    customSoilMix: generateCustomSoilMix(characteristics, plantName)
  };
}

// Analyze plant characteristics based on botanical classification
function analyzePlantCharacteristics(family: string, genus: string, scientificName: string) {
  const characteristics = {
    type: "houseplant",
    drainageNeeds: "moderate",
    lightNeeds: "bright-indirect",
    waterFrequency: "moderate",
    humidityNeeds: "average",
    growthHabit: "upright",
    soilType: "standard",
    fertilityNeeds: "moderate"
  };

  // Araceae family (Philodendrons, Monsteras, Pothos, etc.)
  if (family.includes("araceae")) {
    characteristics.type = "aroid";
    characteristics.drainageNeeds = "high";
    characteristics.lightNeeds = "bright-indirect";
    characteristics.waterFrequency = "moderate";
    characteristics.humidityNeeds = "high";
    characteristics.growthHabit = genus.includes("monstera") ? "climbing" : "trailing";
    characteristics.soilType = "chunky-aroid";
    characteristics.fertilityNeeds = "moderate";
  }

  // Cactaceae family (Cacti)
  else if (family.includes("cactaceae")) {
    characteristics.type = "succulent";
    characteristics.drainageNeeds = "very-high";
    characteristics.lightNeeds = "bright-direct";
    characteristics.waterFrequency = "low";
    characteristics.humidityNeeds = "low";
    characteristics.growthHabit = "upright";
    characteristics.soilType = "cactus";
    characteristics.fertilityNeeds = "low";
  }

  // Crassulaceae family (Succulents)
  else if (family.includes("crassulaceae")) {
    characteristics.type = "succulent";
    characteristics.drainageNeeds = "high";
    characteristics.lightNeeds = "bright-indirect";
    characteristics.waterFrequency = "low";
    characteristics.humidityNeeds = "low";
    characteristics.growthHabit = "compact";
    characteristics.soilType = "succulent";
    characteristics.fertilityNeeds = "low";
  }

  // Asparagaceae family (Snake plants, ZZ plants)
  else if (family.includes("asparagaceae")) {
    characteristics.type = "drought-tolerant";
    characteristics.drainageNeeds = "high";
    characteristics.lightNeeds = "low-to-bright";
    characteristics.waterFrequency = "low";
    characteristics.humidityNeeds = "low";
    characteristics.growthHabit = "upright";
    characteristics.soilType = "well-draining";
    characteristics.fertilityNeeds = "low";
  }

  // Moraceae family (Ficus/Rubber trees)
  else if (family.includes("moraceae")) {
    characteristics.type = "tree";
    characteristics.drainageNeeds = "moderate";
    characteristics.lightNeeds = "bright-indirect";
    characteristics.waterFrequency = "moderate";
    characteristics.humidityNeeds = "moderate";
    characteristics.growthHabit = "upright";
    characteristics.soilType = "standard";
    characteristics.fertilityNeeds = "moderate";
  }

  // Ferns (various families)
  else if (scientificName.toLowerCase().includes("fern") || family.includes("polypodiaceae") || family.includes("pteridaceae")) {
    characteristics.type = "fern";
    characteristics.drainageNeeds = "moderate";
    characteristics.lightNeeds = "low-to-medium";
    characteristics.waterFrequency = "high";
    characteristics.humidityNeeds = "very-high";
    characteristics.growthHabit = "clumping";
    characteristics.soilType = "moisture-retaining";
    characteristics.fertilityNeeds = "low";
  }

  return characteristics;
}

// Generate specific light requirements
function generateLightRequirements(characteristics: any, plantName: string): string {
  switch (characteristics.lightNeeds) {
    case "bright-direct":
      return `${plantName} thrives in bright, direct sunlight. Place near a south-facing window or provide 6+ hours of direct sun daily. Can handle intense light and heat.`;
    case "bright-indirect":
      return `${plantName} prefers bright, indirect light. Place near an east or west-facing window with filtered light. Avoid direct afternoon sun which can scorch leaves.`;
    case "low-to-bright":
      return `${plantName} is very adaptable to light conditions, from low light to bright indirect light. Perfect for offices or rooms with limited natural light.`;
    case "low-to-medium":
      return `${plantName} prefers low to medium indirect light. Ideal for north-facing windows or areas away from direct sunlight. Too much light can damage fronds.`;
    default:
      return `${plantName} grows best in bright, indirect light. Avoid direct sunlight which can damage the leaves.`;
  }
}

// Generate specific water requirements
function generateWaterRequirements(characteristics: any, plantName: string): string {
  switch (characteristics.waterFrequency) {
    case "low":
      return `Water ${plantName} sparingly. Allow soil to dry completely between waterings, typically every 2-4 weeks. Overwatering is the most common cause of problems.`;
    case "moderate":
      return `Water ${plantName} when the top 1-2 inches of soil feel dry. Typically every 1-2 weeks, but adjust based on season and humidity levels.`;
    case "high":
      return `Keep ${plantName} consistently moist but not waterlogged. Water when the top inch of soil feels slightly dry, usually every 3-5 days.`;
    default:
      return `Water ${plantName} when the top inch of soil feels dry, typically every 1-2 weeks.`;
  }
}

// Generate specific humidity requirements
function generateHumidityRequirements(characteristics: any, plantName: string): string {
  switch (characteristics.humidityNeeds) {
    case "very-high":
      return `${plantName} requires very high humidity (60-80%). Use a humidifier, group with other plants, or place on a pebble tray. Mist regularly but avoid water on fronds.`;
    case "high":
      return `${plantName} prefers high humidity (50-60%). Use a humidifier, pebble tray, or group with other plants. Mist regularly but avoid water on leaves.`;
    case "moderate":
      return `${plantName} does well with moderate humidity (40-50%). Average home humidity is usually sufficient, but may benefit from occasional misting.`;
    case "low":
      return `${plantName} tolerates low humidity well. Average home humidity (30-40%) is perfectly fine. Avoid overhumidifying which can cause problems.`;
    default:
      return `${plantName} prefers moderate humidity (40-50%). Use a humidifier or pebble tray if your home is very dry.`;
  }
}

// Generate specific temperature requirements
function generateTemperatureRequirements(characteristics: any, plantName: string): string {
  switch (characteristics.type) {
    case "succulent":
      return `${plantName} prefers warm temperatures between 65-80°F (18-27°C). Can tolerate cooler temperatures down to 50°F (10°C) but avoid frost.`;
    case "aroid":
      return `${plantName} thrives in warm temperatures between 65-80°F (18-27°C). Avoid cold drafts and sudden temperature changes which can cause stress.`;
    case "fern":
      return `${plantName} prefers cool to moderate temperatures between 60-75°F (15-24°C). Avoid hot, dry conditions and maintain consistent temperatures.`;
    case "drought-tolerant":
      return `${plantName} is very tolerant of temperature fluctuations, handling 60-85°F (15-29°C). Can survive brief periods outside this range.`;
    default:
      return `${plantName} prefers stable temperatures between 65-75°F (18-24°C). Avoid cold drafts and sudden temperature changes.`;
  }
}

// Generate specific soil requirements
function generateSoilRequirements(characteristics: any, plantName: string): string {
  switch (characteristics.soilType) {
    case "chunky-aroid":
      return `${plantName} needs a chunky, well-draining aroid mix. Combine bark, perlite, and peat moss or coco coir for excellent drainage and aeration.`;
    case "cactus":
      return `${plantName} requires extremely well-draining cactus soil. Use a commercial cactus mix or add extra perlite and sand to regular potting soil.`;
    case "succulent":
      return `${plantName} needs well-draining succulent soil. Use a commercial succulent mix or add perlite to regular potting soil for better drainage.`;
    case "well-draining":
      return `${plantName} requires well-draining soil that doesn't stay soggy. Add perlite or bark to regular potting mix to improve drainage.`;
    case "moisture-retaining":
      return `${plantName} prefers soil that retains moisture but doesn't become waterlogged. Use a peat-based mix with good organic content.`;
    default:
      return `${plantName} grows well in standard well-draining potting mix. Ensure good drainage to prevent root rot.`;
  }
}

// Generate specific fertilizer requirements
function generateFertilizerRequirements(characteristics: any, plantName: string): string {
  switch (characteristics.fertilityNeeds) {
    case "low":
      return `${plantName} has low fertilizer needs. Feed sparingly 2-3 times per year during growing season with diluted liquid fertilizer.`;
    case "moderate":
      return `${plantName} benefits from regular feeding during growing season (spring/summer). Use balanced liquid fertilizer monthly, diluted to half strength.`;
    case "high":
      return `${plantName} is a heavy feeder during growing season. Fertilize every 2-3 weeks with balanced liquid fertilizer from spring through summer.`;
    default:
      return `Feed ${plantName} monthly during growing season (spring/summer) with balanced liquid fertilizer diluted to half strength.`;
  }
}

// Generate specific repotting requirements
function generateRepottingRequirements(characteristics: any, plantName: string): string {
  switch (characteristics.growthHabit) {
    case "climbing":
      return `Repot ${plantName} every 2-3 years or when rootbound. Provide a moss pole or trellis for climbing support. Spring is the best time for repotting.`;
    case "trailing":
      return `Repot ${plantName} every 2-3 years or when roots grow out of drainage holes. Choose a hanging basket or allow to trail from a shelf.`;
    case "compact":
      return `${plantName} prefers to be slightly rootbound. Repot every 3-4 years or when severely rootbound, using a pot only slightly larger.`;
    case "clumping":
      return `Repot ${plantName} every 2-3 years or when the clump outgrows its container. Divide large clumps during repotting if desired.`;
    default:
      return `Repot ${plantName} every 2-3 years or when rootbound. Choose a pot only 1-2 inches larger than the current one.`;
  }
}

// Generate common issues based on plant characteristics
function generateCommonIssues(characteristics: any, plantName: string): string[] {
  const issues = [];

  // Base issues for all plants
  if (characteristics.waterFrequency === "low") {
    issues.push("Overwatering (most common cause of problems)");
    issues.push("Soft/mushy stems or leaves (overwatering)");
  } else {
    issues.push("Yellow leaves (often overwatering)");
    issues.push("Brown leaf tips (underwatering or low humidity)");
  }

  // Type-specific issues
  switch (characteristics.type) {
    case "aroid":
      issues.push("Leggy growth (insufficient light)");
      issues.push("Small leaves (needs climbing support)");
      break;
    case "succulent":
      issues.push("Stretching/etiolation (insufficient light)");
      issues.push("Root rot (overwatering)");
      break;
    case "fern":
      issues.push("Brown, crispy fronds (low humidity)");
      issues.push("Slow growth (normal in winter)");
      break;
    case "drought-tolerant":
      issues.push("Slow growth (normal characteristic)");
      issues.push("Leaf drop (stress from changes)");
      break;
    default:
      issues.push("Pest issues (spider mites, aphids)");
      issues.push("Slow growth (normal in winter)");
  }

  return issues;
}

// Generate care tips based on plant characteristics
function generateCareTips(characteristics: any, plantName: string): string[] {
  const tips = [];

  // Type-specific tips
  switch (characteristics.type) {
    case "aroid":
      tips.push("Wipe leaves regularly to keep them glossy");
      tips.push("Provide climbing support for best growth");
      tips.push("Propagate easily in water or soil");
      break;
    case "succulent":
      tips.push("Perfect for beginners and busy plant parents");
      tips.push("Can survive neglect better than overcare");
      tips.push("Propagate from leaf cuttings or offsets");
      break;
    case "fern":
      tips.push("Group with other plants to increase humidity");
      tips.push("Avoid touching fronds as they're delicate");
      tips.push("Remove brown fronds to encourage new growth");
      break;
    case "drought-tolerant":
      tips.push("Extremely low maintenance and forgiving");
      tips.push("Perfect for low-light areas");
      tips.push("Divide clumps to propagate new plants");
      break;
    default:
      tips.push("Observe your plant's response to adjust care");
      tips.push("Rotate regularly for even growth");
      tips.push("Clean leaves monthly for better photosynthesis");
  }

  // Growth habit tips
  if (characteristics.growthHabit === "trailing") {
    tips.push("Trim regularly to maintain shape and fullness");
  } else if (characteristics.growthHabit === "climbing") {
    tips.push("Aerial roots are normal and help with climbing");
  }

  return tips;
}

// Generate custom soil mix based on plant characteristics
function generateCustomSoilMix(characteristics: any, plantName: string): any {
  const baseDescription = `Custom soil mix optimized for ${plantName} based on its specific needs`;

  switch (characteristics.soilType) {
    case "chunky-aroid":
      return {
        description: `${baseDescription} - chunky, well-draining mix perfect for epiphytic growth`,
        totalVolume: "100 liters (5 buckets of 20L each)",
        bucketMeasurements: [
          { material: "Old soil", buckets: 1, liters: 20, purpose: "Base structure and stability" },
          { material: "Compost", buckets: 1.5, liters: 30, purpose: "Rich nutrients for vigorous growth" },
          { material: "Coconut husk chunks", buckets: 1.5, liters: 30, purpose: "Excellent drainage and chunky texture" },
          { material: "Coconut coir", buckets: 0.75, liters: 15, purpose: "Moisture retention without waterlogging" },
          { material: "Small sticks", buckets: 0.25, liters: 5, purpose: "Extra drainage and aeration" }
        ],
        layering: {
          bottom: ["Fallen coconut fronds big", "Small sticks", "Coconut husk chunks"],
          middle: ["Mixed soil blend", "Compost", "Coconut husk chunks"],
          top: ["Coconut coir", "Fallen coconut fronds chopped"]
        },
        preparation: [
          "Create chunky mix by combining all materials loosely",
          "Add 3-4 tablespoons liquid microbial fertilizer for root health",
          "Mix in 1.5 tablespoons fish emulsion per 20L bucket",
          "Ensure mix is chunky, not compacted"
        ],
        notes: [
          "Chunky texture supports aerial root attachment",
          `Excellent for ${plantName} and similar epiphytic plants`,
          "Provides stability for climbing growth"
        ]
      };

    case "cactus":
    case "succulent":
      return {
        description: `${baseDescription} - fast-draining mix perfect for drought-tolerant plants`,
        totalVolume: "100 liters (5 buckets of 20L each)",
        bucketMeasurements: [
          { material: "Old soil", buckets: 1.5, liters: 30, purpose: "Base structure with existing drainage" },
          { material: "Coconut husk chunks", buckets: 2, liters: 40, purpose: "Excellent drainage and aeration" },
          { material: "Compost", buckets: 0.75, liters: 15, purpose: "Light nutrition without water retention" },
          { material: "Small sticks", buckets: 0.5, liters: 10, purpose: "Extra drainage and air pockets" },
          { material: "Dried leaves", buckets: 0.25, liters: 5, purpose: "Light mulch layer" }
        ],
        layering: {
          bottom: ["Fallen coconut fronds big", "Small sticks", "Coconut husk chunks"],
          middle: ["Mixed soil blend", "Coconut husk chunks", "Light compost"],
          top: ["Dried leaves", "Small coconut husk pieces"]
        },
        preparation: [
          "Create very well-draining mix with chunky materials",
          "Add only 1 tablespoon liquid microbial fertilizer (light feeding)",
          "Add 0.5 tablespoon fish emulsion per 20L bucket (minimal nutrition)",
          "Ensure mix drains quickly when watered"
        ],
        notes: [
          "Extremely well-draining to prevent root rot",
          `Low nutrition mix suits ${plantName}'s drought-tolerant nature`,
          "Perfect for neglect-tolerant care style"
        ]
      };

    case "moisture-retaining":
      return {
        description: `${baseDescription} - moisture-retaining mix perfect for humidity-loving plants`,
        totalVolume: "100 liters (5 buckets of 20L each)",
        bucketMeasurements: [
          { material: "Old soil", buckets: 1.25, liters: 25, purpose: "Base structure and nutrients" },
          { material: "Compost", buckets: 1.5, liters: 30, purpose: "Rich organic matter and moisture retention" },
          { material: "Coconut coir", buckets: 2, liters: 40, purpose: "Excellent moisture retention" },
          { material: "Dried leaves", buckets: 0.25, liters: 5, purpose: "Natural mulch and slow decomposition" }
        ],
        layering: {
          bottom: ["Small sticks", "Fallen coconut fronds chopped"],
          middle: ["Mixed soil blend", "Compost", "Coconut coir"],
          top: ["Dried leaves", "Thick coconut coir layer"]
        },
        preparation: [
          "Mix all materials to create moisture-retentive blend",
          "Add 3 tablespoons liquid microbial fertilizer",
          "Add 1.5 tablespoons fish emulsion per 20L bucket",
          "Ensure good moisture retention without waterlogging"
        ],
        notes: [
          "Retains moisture while still allowing drainage",
          `Perfect for ${plantName}'s humidity requirements`,
          "Monitor watering as soil stays moist longer"
        ]
      };

    default:
      return {
        description: `${baseDescription} - balanced, versatile mix suitable for most houseplants`,
        totalVolume: "100 liters (5 buckets of 20L each)",
        bucketMeasurements: [
          { material: "Old soil", buckets: 1.5, liters: 30, purpose: "Base structure and existing nutrients" },
          { material: "Compost", buckets: 1.25, liters: 25, purpose: "Rich organic matter for healthy growth" },
          { material: "Coconut coir", buckets: 1.25, liters: 25, purpose: "Moisture retention and root support" },
          { material: "Coconut husk chunks", buckets: 0.75, liters: 15, purpose: "Drainage and aeration" },
          { material: "Dried leaves", buckets: 0.25, liters: 5, purpose: "Natural mulch and slow decomposition" }
        ],
        layering: {
          bottom: ["Small sticks", "Fallen coconut fronds chopped", "Coconut husk chunks"],
          middle: ["Mixed soil blend", "Compost", "Coconut coir"],
          top: ["Dried leaves", "Light coconut coir layer"]
        },
        preparation: [
          "Mix all materials thoroughly for even distribution",
          "Add 2-3 tablespoons liquid microbial fertilizer",
          "Add 1 tablespoon fish emulsion per 20L bucket",
          "Let mixture settle for 24 hours before use"
        ],
        notes: [
          `Versatile mix suitable for ${plantName} and similar plants`,
          "Good balance of drainage and moisture retention",
          "Adjust ratios based on specific plant response"
        ]
      };
  }
}

// Call Plant.id API for plant identification
async function identifyPlantWithPlantId(imageBase64: string): Promise<PlantIdResponse> {
  const apiKey = process.env.PLANT_ID_API_KEY;

  // Check if API key is configured
  if (!apiKey || apiKey === "your_plant_id_api_key_here") {
    throw new Error("Plant.id API key not configured. Please add your API key to the .env file.");
  }

  const requestBody = {
    images: [imageBase64],
    similar_images: true,
    // Health assessment: "auto" includes health only if plant appears diseased
    health: "auto",
    // Custom ID for tracking this identification
    custom_id: Math.floor(Math.random() * 1000000)
    // Note: Removed plant_details parameter as it's not valid in API v3
    // Note: Removed datetime parameter as it was causing format issues
    // Geographic coordinates can improve identification accuracy
    // Uncomment and set if user location is available:
    // latitude: 37.7749,
    // longitude: -122.4194
  };

  console.log("Sending request to Plant.id API...");
  console.log("Request body:", JSON.stringify(requestBody, null, 2));

  try {
    const response = await fetch("https://api.plant.id/v3/identification", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Api-Key": apiKey,
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Plant.id API error:", {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
        body: errorText
      });

      // Handle specific API errors
      if (response.status === 401) {
        throw new Error("Invalid Plant.id API key. Please check your API key in the .env file.");
      }

      if (response.status === 402) {
        throw new Error("Plant.id API quota exceeded. Please check your account credits.");
      }

      if (response.status === 400) {
        throw new Error("Invalid request format. Please try with a different image.");
      }

      throw new Error(`Plant.id API error: ${response.status} ${errorText}`);
    }

    const result: PlantIdResponse = await response.json();
    console.log("✅ Plant.id API response received:", {
      access_token: result.access_token,
      is_plant_probability: result.result?.is_plant?.probability,
      suggestions_count: result.result?.classification?.suggestions?.length || 0
    });
    return result;

  } catch (error) {
    console.error("Plant.id API request failed:", error);
    throw error; // Re-throw the error instead of falling back to mock
  }
}

// Process Plant.id response into our enhanced format with care guide
async function processPlantIdResponse(plantIdResponse: PlantIdResponse): Promise<PlantIdentificationResult> {
  // Handle both new API v3 format and legacy format
  const suggestions = plantIdResponse.result?.classification?.suggestions || plantIdResponse.suggestions || [];
  const isPlantProbability = plantIdResponse.result?.is_plant?.probability || plantIdResponse.is_plant_probability || 0;
  const responseId = plantIdResponse.access_token || plantIdResponse.id?.toString() || 'unknown';

  if (suggestions.length === 0) {
    throw new Error("No plant suggestions found");
  }

  const topSuggestion = suggestions[0];
  const scientificName = topSuggestion.name;
  const commonNames = topSuggestion.details?.common_names || [];
  const plantName = commonNames[0] || scientificName;

  // Generate comprehensive care guide
  const careGuide = await generateCareGuide(scientificName, commonNames, topSuggestion.details?.taxonomy, topSuggestion.details?.description?.value);

  const topMatches = suggestions.slice(0, 3).map(suggestion => ({
    name: suggestion.details?.common_names?.[0] || suggestion.name,
    scientificName: suggestion.name,
    confidence: suggestion.probability,
    image: suggestion.similar_images?.[0]?.url_small
  }));

  // Get the best available image
  const plantImage = topSuggestion.similar_images?.[0]?.url || topSuggestion.similar_images?.[0]?.url_small;

  return {
    id: `plant-${responseId}`,
    name: plantName,
    scientificName: scientificName,
    commonNames: commonNames,
    confidence: topSuggestion.probability,
    description: topSuggestion.details?.description?.value || `${plantName} is a beautiful plant that makes an excellent addition to any home or garden.`,
    image: plantImage,
    similarImages: topSuggestion.similar_images?.slice(0, 5).map(img => ({
      url: img.url_small || img.url,
      similarity: img.similarity || 0.8
    })) || [],
    taxonomy: {
      family: topSuggestion.details?.taxonomy?.family,
      genus: topSuggestion.details?.taxonomy?.genus,
      class: topSuggestion.details?.taxonomy?.class
    },
    isPlantProbability: isPlantProbability,
    topMatches,
    careGuide
  };
}

export async function action({ request }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }

  try {
    const formData = await request.formData();
    const imageData = formData.get("image") as string;

    if (!imageData) {
      return json({ error: "No image provided" }, { status: 400 });
    }

    // Validate base64 image format
    if (!imageData.startsWith("data:image/")) {
      return json({ error: "Invalid image format" }, { status: 400 });
    }

    // Extract base64 data from data URL
    const base64Data = imageData.split(",")[1];
    if (!base64Data) {
      return json({ error: "Invalid image format" }, { status: 400 });
    }

    console.log("Starting Plant.id identification...");
    console.log("API Key configured:", !!process.env.PLANT_ID_API_KEY);
    console.log("Image size:", base64Data.length, "characters");

    // Call Plant.id API
    const plantIdResponse = await identifyPlantWithPlantId(base64Data);

    // Handle both new API v3 format and legacy format
    const isPlantProbability = plantIdResponse.result?.is_plant?.probability || plantIdResponse.is_plant_probability || 0;
    const suggestions = plantIdResponse.result?.classification?.suggestions || plantIdResponse.suggestions || [];

    console.log("Plant.id response received, is_plant_probability:", isPlantProbability);
    console.log("Number of suggestions:", suggestions.length);

    // Check if it's likely a plant
    if (isPlantProbability < 0.5) {
      return json({
        success: false,
        error: "This doesn't appear to be a plant. Please try uploading a clearer photo of a plant.",
        isPlantProbability: isPlantProbability
      });
    }

    // Process the response
    const result = await processPlantIdResponse(plantIdResponse);

    console.log("Plant identification completed:", result.name, `(${Math.round(result.confidence * 100)}%)`);

    return json({
      success: true,
      result,
      lowConfidence: result.confidence < 0.7
    });

  } catch (error) {
    console.error("Plant identification error details:", {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      apiKeyExists: !!process.env.PLANT_ID_API_KEY
    });

    if (error instanceof Error && error.message.includes("Plant.id API key")) {
      return json(
        { error: "Plant identification service is not configured. Please check API key." },
        { status: 500 }
      );
    }

    if (error instanceof Error && error.message.includes("Plant.id API error")) {
      return json(
        { error: `Plant.id service error: ${error.message}` },
        { status: 500 }
      );
    }

    return json(
      { error: "Failed to identify plant. Please try again with a clearer photo." },
      { status: 500 }
    );
  }
}
