import { json, type ActionFunctionArgs } from "@remix-run/node";

// Plant.id API response interfaces
interface PlantIdSuggestion {
  id: number;
  name: string;
  probability: number;
  confirmed: boolean;
  similar_images: Array<{
    id: string;
    url: string;
    license_name?: string;
    license_url?: string;
    citation?: string;
    similarity: number;
    url_small: string;
  }>;
  details: {
    common_names?: string[];
    url?: string;
    description?: {
      value: string;
      citation?: string;
    };
    taxonomy?: {
      class?: string;
      family?: string;
      genus?: string;
      kingdom?: string;
      order?: string;
      phylum?: string;
    };
    synonyms?: string[];
  };
}

interface PlantIdResponse {
  id: number;
  custom_id?: string;
  meta_data: {
    latitude?: number;
    longitude?: number;
    date?: string;
    datetime?: string;
  };
  uploaded_datetime: number;
  finished_datetime: number;
  suggestions: PlantIdSuggestion[];
  modifiers: string[];
  secret: string;
  fail_cause?: string;
  countable: boolean;
  feedback?: string;
  is_plant_probability: number;
}

// Our simplified result interface
interface PlantIdentificationResult {
  id: string;
  name: string;
  scientificName: string;
  commonNames: string[];
  confidence: number;
  description?: string;
  image?: string;
  similarImages: Array<{
    url: string;
    similarity: number;
  }>;
  taxonomy?: {
    family?: string;
    genus?: string;
    class?: string;
  };
  isPlantProbability: number;
  topMatches: Array<{
    name: string;
    scientificName: string;
    confidence: number;
    image?: string;
  }>;
}

// Call Plant.id API for plant identification
async function identifyPlantWithPlantId(imageBase64: string): Promise<PlantIdResponse> {
  const apiKey = process.env.PLANT_ID_API_KEY;

  if (!apiKey) {
    throw new Error("Plant.id API key not configured");
  }

  const requestBody = {
    images: [imageBase64],
    modifiers: ["crops_fast", "similar_images"],
    plant_details: ["common_names", "url", "description", "taxonomy", "synonyms"]
  };

  console.log("Sending request to Plant.id API...");

  const response = await fetch("https://plant.id/api/v3/identification", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Api-Key": apiKey,
    },
    body: JSON.stringify(requestBody),
  });

  if (!response.ok) {
    const errorText = await response.text();
    console.error("Plant.id API error:", response.status, errorText);
    throw new Error(`Plant.id API error: ${response.status} ${errorText}`);
  }

  const result: PlantIdResponse = await response.json();
  console.log("Plant.id API response received");

  return result;
}

// Process Plant.id response into our simplified format
function processPlantIdResponse(plantIdResponse: PlantIdResponse): PlantIdentificationResult {
  const suggestions = plantIdResponse.suggestions || [];

  if (suggestions.length === 0) {
    throw new Error("No plant suggestions found");
  }

  const topSuggestion = suggestions[0];
  const topMatches = suggestions.slice(0, 3).map(suggestion => ({
    name: suggestion.details.common_names?.[0] || suggestion.name,
    scientificName: suggestion.name,
    confidence: suggestion.probability,
    image: suggestion.similar_images?.[0]?.url_small
  }));

  return {
    id: `plant-${plantIdResponse.id}`,
    name: topSuggestion.details.common_names?.[0] || topSuggestion.name,
    scientificName: topSuggestion.name,
    commonNames: topSuggestion.details.common_names || [],
    confidence: topSuggestion.probability,
    description: topSuggestion.details.description?.value,
    image: topSuggestion.similar_images?.[0]?.url_small,
    similarImages: topSuggestion.similar_images?.slice(0, 5).map(img => ({
      url: img.url_small,
      similarity: img.similarity
    })) || [],
    taxonomy: {
      family: topSuggestion.details.taxonomy?.family,
      genus: topSuggestion.details.taxonomy?.genus,
      class: topSuggestion.details.taxonomy?.class
    },
    isPlantProbability: plantIdResponse.is_plant_probability,
    topMatches
  };
}

export async function action({ request }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }

  try {
    const formData = await request.formData();
    const imageData = formData.get("image") as string;

    if (!imageData) {
      return json({ error: "No image provided" }, { status: 400 });
    }

    // Validate base64 image format
    if (!imageData.startsWith("data:image/")) {
      return json({ error: "Invalid image format" }, { status: 400 });
    }

    // Extract base64 data from data URL
    const base64Data = imageData.split(",")[1];
    if (!base64Data) {
      return json({ error: "Invalid image format" }, { status: 400 });
    }

    console.log("Starting Plant.id identification...");
    console.log("API Key configured:", !!process.env.PLANT_ID_API_KEY);
    console.log("Image size:", base64Data.length, "characters");

    // Call Plant.id API
    const plantIdResponse = await identifyPlantWithPlantId(base64Data);

    console.log("Plant.id response received, is_plant_probability:", plantIdResponse.is_plant_probability);
    console.log("Number of suggestions:", plantIdResponse.suggestions?.length || 0);

    // Check if it's likely a plant
    if (plantIdResponse.is_plant_probability < 0.5) {
      return json({
        success: false,
        error: "This doesn't appear to be a plant. Please try uploading a clearer photo of a plant.",
        isPlantProbability: plantIdResponse.is_plant_probability
      });
    }

    // Process the response
    const result = processPlantIdResponse(plantIdResponse);

    console.log("Plant identification completed:", result.name, `(${Math.round(result.confidence * 100)}%)`);

    return json({
      success: true,
      result,
      lowConfidence: result.confidence < 0.7
    });

  } catch (error) {
    console.error("Plant identification error details:", {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      apiKeyExists: !!process.env.PLANT_ID_API_KEY
    });

    if (error instanceof Error && error.message.includes("Plant.id API key")) {
      return json(
        { error: "Plant identification service is not configured. Please check API key." },
        { status: 500 }
      );
    }

    if (error instanceof Error && error.message.includes("Plant.id API error")) {
      return json(
        { error: `Plant.id service error: ${error.message}` },
        { status: 500 }
      );
    }

    return json(
      { error: "Failed to identify plant. Please try again with a clearer photo." },
      { status: 500 }
    );
  }
}
