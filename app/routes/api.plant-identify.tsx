import { json, type ActionFunctionArgs } from "@remix-run/node";

// Plant.id API response interfaces
interface PlantIdSuggestion {
  id: number;
  name: string;
  probability: number;
  confirmed: boolean;
  similar_images: Array<{
    id: string;
    url: string;
    license_name?: string;
    license_url?: string;
    citation?: string;
    similarity: number;
    url_small: string;
  }>;
  details: {
    common_names?: string[];
    url?: string;
    description?: {
      value: string;
      citation?: string;
    };
    taxonomy?: {
      class?: string;
      family?: string;
      genus?: string;
      kingdom?: string;
      order?: string;
      phylum?: string;
    };
    synonyms?: string[];
  };
}

interface PlantIdResponse {
  access_token: string;
  result: {
    is_plant: {
      binary: boolean;
      probability: number;
    };
    classification: {
      suggestions: PlantIdSuggestion[];
    };
  };
  // Legacy fields for backward compatibility
  id?: number;
  suggestions?: PlantIdSuggestion[];
  is_plant_probability?: number;
}

// Our simplified result interface
interface PlantIdentificationResult {
  id: string;
  name: string;
  scientificName: string;
  commonNames: string[];
  confidence: number;
  description?: string;
  image?: string;
  similarImages: Array<{
    url: string;
    similarity: number;
  }>;
  taxonomy?: {
    family?: string;
    genus?: string;
    class?: string;
  };
  isPlantProbability: number;
  topMatches: Array<{
    name: string;
    scientificName: string;
    confidence: number;
    image?: string;
  }>;
}

// Mock plant identification for testing (remove when you have a valid API key)
function createMockPlantResponse(): PlantIdResponse {
  const mockPlants = [
    {
      name: "Monstera deliciosa",
      commonNames: ["Swiss Cheese Plant", "Split-leaf Philodendron"],
      description: "A popular houseplant known for its large, glossy leaves with natural holes. Native to Central America, it's prized for its dramatic foliage and easy care requirements.",
      confidence: 0.85
    },
    {
      name: "Ficus lyrata",
      commonNames: ["Fiddle Leaf Fig", "Banjo Fig"],
      description: "A trendy houseplant with large, violin-shaped leaves. Originally from western Africa, it has become one of the most popular indoor plants.",
      confidence: 0.78
    },
    {
      name: "Sansevieria trifasciata",
      commonNames: ["Snake Plant", "Mother-in-Law's Tongue"],
      description: "A hardy succulent with upright, sword-like leaves. Known for its air-purifying qualities and tolerance to neglect.",
      confidence: 0.92
    },
    {
      name: "Pothos aureus",
      commonNames: ["Golden Pothos", "Devil's Ivy"],
      description: "A trailing vine with heart-shaped leaves. One of the easiest houseplants to grow and propagate.",
      confidence: 0.88
    }
  ];

  const selectedPlant = mockPlants[Math.floor(Math.random() * mockPlants.length)];

  const suggestions = [
    {
      id: 1,
      name: selectedPlant.name,
      probability: selectedPlant.confidence,
      confirmed: false,
      details: {
        common_names: selectedPlant.commonNames,
        description: { value: selectedPlant.description },
        taxonomy: {
          family: "Araceae",
          genus: selectedPlant.name.split(" ")[0],
          class: "Liliopsida"
        }
      },
      similar_images: [
        {
          id: "1",
          url: "https://images.unsplash.com/photo-1586093248292-4e6636ce8b97?w=600",
          url_small: "https://images.unsplash.com/photo-1586093248292-4e6636ce8b97?w=300",
          similarity: 0.9
        },
        {
          id: "2",
          url: "https://images.unsplash.com/photo-1592150621744-aca64f48394a?w=600",
          url_small: "https://images.unsplash.com/photo-1592150621744-aca64f48394a?w=300",
          similarity: 0.8
        },
        {
          id: "3",
          url: "https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=600",
          url_small: "https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=300",
          similarity: 0.75
        }
      ]
    },
    // Add second and third matches
    ...mockPlants.filter(p => p.name !== selectedPlant.name).slice(0, 2).map((plant, index) => ({
      id: index + 2,
      name: plant.name,
      probability: plant.confidence - 0.1,
      confirmed: false,
      details: {
        common_names: plant.commonNames,
        description: { value: plant.description },
        taxonomy: {
          family: "Araceae",
          genus: plant.name.split(" ")[0],
          class: "Liliopsida"
        }
      },
      similar_images: [
        {
          id: `${index + 4}`,
          url: "https://images.unsplash.com/photo-1586093248292-4e6636ce8b97?w=600",
          url_small: "https://images.unsplash.com/photo-1586093248292-4e6636ce8b97?w=300",
          similarity: 0.8 - index * 0.1
        }
      ]
    }))
  ];

  return {
    access_token: Math.random().toString(36).substr(2, 16),
    result: {
      is_plant: {
        binary: true,
        probability: 0.95
      },
      classification: {
        suggestions: suggestions
      }
    },
    // Legacy fields for backward compatibility
    id: Math.floor(Math.random() * 1000000),
    suggestions: suggestions,
    is_plant_probability: 0.95
  };
}

// Call Plant.id API for plant identification
async function identifyPlantWithPlantId(imageBase64: string): Promise<PlantIdResponse> {
  const apiKey = process.env.PLANT_ID_API_KEY;

  // If no API key or placeholder key, use mock response
  if (!apiKey || apiKey === "your_plant_id_api_key_here") {
    console.log("🌿 Using mock plant identification (no valid API key configured)");
    console.log("📝 To use real Plant.id API: Get a key from https://plant.id/ and update your .env file");
    await new Promise(resolve => setTimeout(resolve, 1500)); // Simulate API delay
    return createMockPlantResponse();
  }

  const requestBody = {
    images: [imageBase64],
    similar_images: true,
    plant_details: ["common_names", "url", "description", "taxonomy", "synonyms"],
    classification_level: "species",
    // Add health assessment for comprehensive plant analysis
    health: "auto",
    // Add geographic context if available (could be added from user location)
    // latitude: 37.7749,  // Example: San Francisco
    // longitude: -122.4194,
    // Custom ID for tracking
    custom_id: Math.floor(Math.random() * 1000000)
  };

  console.log("Sending request to Plant.id API...");

  try {
    const response = await fetch("https://plant.id/api/v3/identification", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Api-Key": apiKey,
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Plant.id API error:", response.status, errorText);

      // If API key is invalid (401), fall back to mock response
      if (response.status === 401) {
        console.log("🔑 Invalid API key, falling back to mock response");
        console.log("📝 Get a valid API key from https://plant.id/ and update your .env file");
        return createMockPlantResponse();
      }

      throw new Error(`Plant.id API error: ${response.status} ${errorText}`);
    }

    const result: PlantIdResponse = await response.json();
    console.log("✅ Plant.id API response received");
    return result;

  } catch (error) {
    console.error("Plant.id API request failed:", error);
    console.log("🔄 Falling back to mock response");
    return createMockPlantResponse();
  }
}

// Process Plant.id response into our simplified format
function processPlantIdResponse(plantIdResponse: PlantIdResponse): PlantIdentificationResult {
  // Handle both new API v3 format and legacy format
  const suggestions = plantIdResponse.result?.classification?.suggestions || plantIdResponse.suggestions || [];
  const isPlantProbability = plantIdResponse.result?.is_plant?.probability || plantIdResponse.is_plant_probability || 0;
  const responseId = plantIdResponse.access_token || plantIdResponse.id?.toString() || 'unknown';

  if (suggestions.length === 0) {
    throw new Error("No plant suggestions found");
  }

  const topSuggestion = suggestions[0];
  const topMatches = suggestions.slice(0, 3).map(suggestion => ({
    name: suggestion.details.common_names?.[0] || suggestion.name,
    scientificName: suggestion.name,
    confidence: suggestion.probability,
    image: suggestion.similar_images?.[0]?.url_small
  }));

  return {
    id: `plant-${responseId}`,
    name: topSuggestion.details.common_names?.[0] || topSuggestion.name,
    scientificName: topSuggestion.name,
    commonNames: topSuggestion.details.common_names || [],
    confidence: topSuggestion.probability,
    description: topSuggestion.details.description?.value,
    image: topSuggestion.similar_images?.[0]?.url_small,
    similarImages: topSuggestion.similar_images?.slice(0, 5).map(img => ({
      url: img.url_small,
      similarity: img.similarity
    })) || [],
    taxonomy: {
      family: topSuggestion.details.taxonomy?.family,
      genus: topSuggestion.details.taxonomy?.genus,
      class: topSuggestion.details.taxonomy?.class
    },
    isPlantProbability: isPlantProbability,
    topMatches
  };
}

export async function action({ request }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }

  try {
    const formData = await request.formData();
    const imageData = formData.get("image") as string;

    if (!imageData) {
      return json({ error: "No image provided" }, { status: 400 });
    }

    // Validate base64 image format
    if (!imageData.startsWith("data:image/")) {
      return json({ error: "Invalid image format" }, { status: 400 });
    }

    // Extract base64 data from data URL
    const base64Data = imageData.split(",")[1];
    if (!base64Data) {
      return json({ error: "Invalid image format" }, { status: 400 });
    }

    console.log("Starting Plant.id identification...");
    console.log("API Key configured:", !!process.env.PLANT_ID_API_KEY);
    console.log("Image size:", base64Data.length, "characters");

    // Call Plant.id API
    const plantIdResponse = await identifyPlantWithPlantId(base64Data);

    // Handle both new API v3 format and legacy format
    const isPlantProbability = plantIdResponse.result?.is_plant?.probability || plantIdResponse.is_plant_probability || 0;
    const suggestions = plantIdResponse.result?.classification?.suggestions || plantIdResponse.suggestions || [];

    console.log("Plant.id response received, is_plant_probability:", isPlantProbability);
    console.log("Number of suggestions:", suggestions.length);

    // Check if it's likely a plant
    if (isPlantProbability < 0.5) {
      return json({
        success: false,
        error: "This doesn't appear to be a plant. Please try uploading a clearer photo of a plant.",
        isPlantProbability: isPlantProbability
      });
    }

    // Process the response
    const result = processPlantIdResponse(plantIdResponse);

    console.log("Plant identification completed:", result.name, `(${Math.round(result.confidence * 100)}%)`);

    return json({
      success: true,
      result,
      lowConfidence: result.confidence < 0.7
    });

  } catch (error) {
    console.error("Plant identification error details:", {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      apiKeyExists: !!process.env.PLANT_ID_API_KEY
    });

    if (error instanceof Error && error.message.includes("Plant.id API key")) {
      return json(
        { error: "Plant identification service is not configured. Please check API key." },
        { status: 500 }
      );
    }

    if (error instanceof Error && error.message.includes("Plant.id API error")) {
      return json(
        { error: `Plant.id service error: ${error.message}` },
        { status: 500 }
      );
    }

    return json(
      { error: "Failed to identify plant. Please try again with a clearer photo." },
      { status: 500 }
    );
  }
}
