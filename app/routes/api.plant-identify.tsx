import { json, type ActionFunctionArgs } from "@remix-run/node";

/**
 * Plant.id API v3 Integration
 *
 * This module integrates with the Plant.id API v3 for AI-powered plant identification.
 *
 * API Documentation: https://documenter.getpostman.com/view/24599534/2s93z5A4v2
 *
 * Key Features:
 * - Plant species identification from images
 * - Similar image matching
 * - Health assessment (auto mode)
 * - Geographic context support
 * - Comprehensive plant details (taxonomy, common names, descriptions)
 *
 * Setup:
 * 1. Sign up at https://plant.id/
 * 2. Get your API key from the dashboard
 * 3. Add PLANT_ID_API_KEY=your_key_here to your .env file
 * 4. Restart your server
 *
 * API Costs:
 * - Basic identification: 1 credit per request
 * - With health assessment: 2 credits per request
 * - Free tier includes limited credits
 */

// Plant.id API v3 response interfaces (based on official documentation)
interface PlantIdSuggestion {
  id: number;
  name: string;
  probability: number;
  confirmed: boolean;
  similar_images?: Array<{
    id: string;
    url: string;
    license_name?: string;
    license_url?: string;
    citation?: string;
    similarity: number;
    url_small: string;
  }>;
  details: {
    common_names?: string[];
    url?: string;
    description?: {
      value: string;
      citation?: string;
    };
    taxonomy?: {
      class?: string;
      family?: string;
      genus?: string;
      kingdom?: string;
      order?: string;
      phylum?: string;
    };
    synonyms?: string[];
  };
}

interface PlantIdResponse {
  access_token: string;
  result: {
    is_plant: {
      binary: boolean;
      probability: number;
    };
    classification: {
      suggestions: PlantIdSuggestion[];
    };
  };
  // Legacy fields for backward compatibility
  id?: number;
  suggestions?: PlantIdSuggestion[];
  is_plant_probability?: number;
}

// Our simplified result interface
interface PlantIdentificationResult {
  id: string;
  name: string;
  scientificName: string;
  commonNames: string[];
  confidence: number;
  description?: string;
  image?: string;
  similarImages: Array<{
    url: string;
    similarity: number;
  }>;
  taxonomy?: {
    family?: string;
    genus?: string;
    class?: string;
  };
  isPlantProbability: number;
  topMatches: Array<{
    name: string;
    scientificName: string;
    confidence: number;
    image?: string;
  }>;
}

// Call Plant.id API for plant identification
async function identifyPlantWithPlantId(imageBase64: string): Promise<PlantIdResponse> {
  const apiKey = process.env.PLANT_ID_API_KEY;

  // Check if API key is configured
  if (!apiKey || apiKey === "your_plant_id_api_key_here") {
    throw new Error("Plant.id API key not configured. Please add your API key to the .env file.");
  }

  const requestBody = {
    images: [imageBase64],
    similar_images: true,
    plant_details: ["common_names", "url", "description", "taxonomy", "synonyms"],
    // Health assessment: "auto" includes health only if plant appears diseased
    health: "auto",
    // Custom ID for tracking this identification
    custom_id: Math.floor(Math.random() * 1000000)
    // Note: Removed datetime parameter as it was causing format issues
    // Geographic coordinates can improve identification accuracy
    // Uncomment and set if user location is available:
    // latitude: 37.7749,
    // longitude: -122.4194
  };

  console.log("Sending request to Plant.id API...");
  console.log("Request body:", JSON.stringify(requestBody, null, 2));

  try {
    const response = await fetch("https://api.plant.id/v3/identification", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Api-Key": apiKey,
      },
      body: JSON.stringify(requestBody),
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Plant.id API error:", {
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
        body: errorText
      });

      // Handle specific API errors
      if (response.status === 401) {
        throw new Error("Invalid Plant.id API key. Please check your API key in the .env file.");
      }

      if (response.status === 402) {
        throw new Error("Plant.id API quota exceeded. Please check your account credits.");
      }

      if (response.status === 400) {
        throw new Error("Invalid request format. Please try with a different image.");
      }

      throw new Error(`Plant.id API error: ${response.status} ${errorText}`);
    }

    const result: PlantIdResponse = await response.json();
    console.log("✅ Plant.id API response received:", {
      access_token: result.access_token,
      is_plant_probability: result.result?.is_plant?.probability,
      suggestions_count: result.result?.classification?.suggestions?.length || 0
    });
    return result;

  } catch (error) {
    console.error("Plant.id API request failed:", error);
    throw error; // Re-throw the error instead of falling back to mock
  }
}

// Process Plant.id response into our simplified format
function processPlantIdResponse(plantIdResponse: PlantIdResponse): PlantIdentificationResult {
  // Handle both new API v3 format and legacy format
  const suggestions = plantIdResponse.result?.classification?.suggestions || plantIdResponse.suggestions || [];
  const isPlantProbability = plantIdResponse.result?.is_plant?.probability || plantIdResponse.is_plant_probability || 0;
  const responseId = plantIdResponse.access_token || plantIdResponse.id?.toString() || 'unknown';

  if (suggestions.length === 0) {
    throw new Error("No plant suggestions found");
  }

  const topSuggestion = suggestions[0];
  const topMatches = suggestions.slice(0, 3).map(suggestion => ({
    name: suggestion.details.common_names?.[0] || suggestion.name,
    scientificName: suggestion.name,
    confidence: suggestion.probability,
    image: suggestion.similar_images?.[0]?.url_small
  }));

  return {
    id: `plant-${responseId}`,
    name: topSuggestion.details.common_names?.[0] || topSuggestion.name,
    scientificName: topSuggestion.name,
    commonNames: topSuggestion.details.common_names || [],
    confidence: topSuggestion.probability,
    description: topSuggestion.details.description?.value,
    image: topSuggestion.similar_images?.[0]?.url_small,
    similarImages: topSuggestion.similar_images?.slice(0, 5).map(img => ({
      url: img.url_small,
      similarity: img.similarity
    })) || [],
    taxonomy: {
      family: topSuggestion.details.taxonomy?.family,
      genus: topSuggestion.details.taxonomy?.genus,
      class: topSuggestion.details.taxonomy?.class
    },
    isPlantProbability: isPlantProbability,
    topMatches
  };
}

export async function action({ request }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }

  try {
    const formData = await request.formData();
    const imageData = formData.get("image") as string;

    if (!imageData) {
      return json({ error: "No image provided" }, { status: 400 });
    }

    // Validate base64 image format
    if (!imageData.startsWith("data:image/")) {
      return json({ error: "Invalid image format" }, { status: 400 });
    }

    // Extract base64 data from data URL
    const base64Data = imageData.split(",")[1];
    if (!base64Data) {
      return json({ error: "Invalid image format" }, { status: 400 });
    }

    console.log("Starting Plant.id identification...");
    console.log("API Key configured:", !!process.env.PLANT_ID_API_KEY);
    console.log("Image size:", base64Data.length, "characters");

    // Call Plant.id API
    const plantIdResponse = await identifyPlantWithPlantId(base64Data);

    // Handle both new API v3 format and legacy format
    const isPlantProbability = plantIdResponse.result?.is_plant?.probability || plantIdResponse.is_plant_probability || 0;
    const suggestions = plantIdResponse.result?.classification?.suggestions || plantIdResponse.suggestions || [];

    console.log("Plant.id response received, is_plant_probability:", isPlantProbability);
    console.log("Number of suggestions:", suggestions.length);

    // Check if it's likely a plant
    if (isPlantProbability < 0.5) {
      return json({
        success: false,
        error: "This doesn't appear to be a plant. Please try uploading a clearer photo of a plant.",
        isPlantProbability: isPlantProbability
      });
    }

    // Process the response
    const result = processPlantIdResponse(plantIdResponse);

    console.log("Plant identification completed:", result.name, `(${Math.round(result.confidence * 100)}%)`);

    return json({
      success: true,
      result,
      lowConfidence: result.confidence < 0.7
    });

  } catch (error) {
    console.error("Plant identification error details:", {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      apiKeyExists: !!process.env.PLANT_ID_API_KEY
    });

    if (error instanceof Error && error.message.includes("Plant.id API key")) {
      return json(
        { error: "Plant identification service is not configured. Please check API key." },
        { status: 500 }
      );
    }

    if (error instanceof Error && error.message.includes("Plant.id API error")) {
      return json(
        { error: `Plant.id service error: ${error.message}` },
        { status: 500 }
      );
    }

    return json(
      { error: "Failed to identify plant. Please try again with a clearer photo." },
      { status: 500 }
    );
  }
}
