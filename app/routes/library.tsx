import { useState } from "react";
import { Link } from "@remix-run/react";
import { MetaFunction } from "@remix-run/node";
import { getAllPlants, getPlantsByDifficulty, Plant } from "../data/plants";
import { ErrorBoundary } from "../components/ErrorBoundary";

export const meta: MetaFunction = () => {
  return [
    { title: "My Plant Library - GardenPal" },
    { name: "description", content: "Browse your plant collection and discover new plants" },
  ];
};

export { ErrorBoundary };

export default function Library() {
  const [filter, setFilter] = useState<"all" | "easy" | "moderate" | "difficult">("all");
  const [searchTerm, setSearchTerm] = useState("");

  const allPlants = getAllPlants();

  const getFilteredPlants = (): Plant[] => {
    let plants = allPlants;

    // Apply difficulty filter
    if (filter !== "all") {
      const difficulty = filter.charAt(0).toUpperCase() + filter.slice(1) as Plant["difficulty"];
      plants = getPlantsByDifficulty(difficulty);
    }

    // Apply search filter
    if (searchTerm) {
      plants = plants.filter(plant =>
        plant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        plant.commonName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        plant.scientificName.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return plants;
  };

  const filteredPlants = getFilteredPlants();

  const getDifficultyColor = (difficulty?: string) => {
    switch (difficulty) {
      case "Easy": return "bg-green-100 text-green-800";
      case "Moderate": return "bg-yellow-100 text-yellow-800";
      case "Difficult": return "bg-red-100 text-red-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getPlantSlug = (plant: Plant): string => {
    return plant.name.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
  };

  return (
    <div className="min-h-screen flex flex-col">
      <header className="bg-emerald-600 text-white p-4 shadow-md">
        <div className="container mx-auto flex justify-between items-center">
          <Link to="/" className="text-2xl font-bold">GardenPal</Link>
          <Link
            to="/identify"
            className="bg-white text-emerald-600 hover:bg-gray-100 font-bold py-1 px-3 rounded-lg text-sm transition-colors"
          >
            Identify Plant
          </Link>
        </div>
      </header>

      <main className="flex-grow container mx-auto p-3 sm:p-4 md:p-6">
        <div className="max-w-6xl mx-auto">
          <div className="mb-6 sm:mb-8">
            <h1 className="text-2xl sm:text-3xl font-bold mb-3 sm:mb-4">My Plant Library</h1>
            <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300 mb-4 sm:mb-6">
              Discover and learn about different plants. Click on any plant to view detailed care information.
            </p>

            {/* Search and Filter Controls */}
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 mb-4 sm:mb-6">
              <div className="flex-1">
                <input
                  type="text"
                  placeholder="Search plants..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full px-3 sm:px-4 py-2 text-sm sm:text-base border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 dark:bg-gray-800 dark:border-gray-600 dark:text-white"
                />
              </div>

              <div className="flex gap-1 sm:gap-2 overflow-x-auto">
                <button
                  onClick={() => setFilter("all")}
                  className={`px-3 sm:px-4 py-2 rounded-lg font-medium transition-colors text-xs sm:text-sm whitespace-nowrap ${
                    filter === "all"
                      ? "bg-emerald-600 text-white"
                      : "bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
                  }`}
                >
                  All
                </button>
                <button
                  onClick={() => setFilter("easy")}
                  className={`px-3 sm:px-4 py-2 rounded-lg font-medium transition-colors text-xs sm:text-sm whitespace-nowrap ${
                    filter === "easy"
                      ? "bg-emerald-600 text-white"
                      : "bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
                  }`}
                >
                  Easy
                </button>
                <button
                  onClick={() => setFilter("moderate")}
                  className={`px-3 sm:px-4 py-2 rounded-lg font-medium transition-colors text-xs sm:text-sm whitespace-nowrap ${
                    filter === "moderate"
                      ? "bg-emerald-600 text-white"
                      : "bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
                  }`}
                >
                  Moderate
                </button>
                <button
                  onClick={() => setFilter("difficult")}
                  className={`px-3 sm:px-4 py-2 rounded-lg font-medium transition-colors text-xs sm:text-sm whitespace-nowrap ${
                    filter === "difficult"
                      ? "bg-emerald-600 text-white"
                      : "bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
                  }`}
                >
                  Difficult
                </button>
              </div>
            </div>
          </div>

          {/* Plants Grid */}
          {filteredPlants.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No plants found</h3>
              <p className="text-gray-500 dark:text-gray-400">Try adjusting your search or filter criteria.</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredPlants.map((plant) => {
                const plantSlug = getPlantSlug(plant);
                return (
                  <Link
                    key={plantSlug}
                    to={`/result/${plantSlug}`}
                    className="group bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow overflow-hidden"
                  >
                    <div className="aspect-w-16 aspect-h-12 bg-gray-200 dark:bg-gray-700">
                      <img
                        src={plant.image}
                        alt={plant.name}
                        className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                      />
                    </div>
                    <div className="p-4">
                      <div className="flex items-start justify-between mb-2">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 group-hover:text-emerald-600 transition-colors">
                          {plant.name}
                        </h3>
                        {plant.difficulty && (
                          <span className={`text-xs px-2 py-1 rounded-full ${getDifficultyColor(plant.difficulty)}`}>
                            {plant.difficulty}
                          </span>
                        )}
                      </div>
                      <p className="text-sm text-gray-500 dark:text-gray-400 italic mb-2">
                        {plant.scientificName}
                      </p>
                      <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-2">
                        {plant.description}
                      </p>
                      <div className="mt-3 flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                        <span>{plant.family}</span>
                        {plant.size && <span>{plant.size}</span>}
                      </div>
                    </div>
                  </Link>
                );
              })}
            </div>
          )}
        </div>
      </main>
    </div>
  );
}
