import { useState, useEffect } from "react";
import { Link } from "@remix-run/react";
import { MetaFunction } from "@remix-run/node";
import { Plant } from "../data/plants";
import { ErrorBoundary } from "../components/ErrorBoundary";
import {
  getAllManagedPlants,
  removePlant,
  isCustomPlant,
  deleteCustomPlant,
  PlantWithId
} from "../utils/plantManager";

export const meta: MetaFunction = () => {
  return [
    { title: "My Plant Library - PlantPal" },
    { name: "description", content: "Browse your plant collection and discover new plants" },
  ];
};

export { ErrorBoundary };

export default function Library() {
  const [filter, setFilter] = useState<"all" | "easy" | "moderate" | "difficult">("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [plants, setPlants] = useState<PlantWithId[]>([]);
  const [showRemoveConfirm, setShowRemoveConfirm] = useState<string | null>(null);

  // Load plants on component mount and when localStorage changes
  useEffect(() => {
    const loadPlants = () => {
      setPlants(getAllManagedPlants());
    };

    loadPlants();

    // Listen for storage changes (in case user has multiple tabs open)
    const handleStorageChange = () => {
      loadPlants();
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  const getFilteredPlants = (): PlantWithId[] => {
    let filteredPlants = plants;

    // Apply difficulty filter
    if (filter !== "all") {
      const difficulty = filter.charAt(0).toUpperCase() + filter.slice(1) as Plant["difficulty"];
      filteredPlants = filteredPlants.filter(plant => plant.difficulty === difficulty);
    }

    // Apply search filter
    if (searchTerm) {
      filteredPlants = filteredPlants.filter(plant =>
        plant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        plant.commonName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        plant.scientificName.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return filteredPlants;
  };

  const filteredPlants = getFilteredPlants();

  // Handle plant removal
  const handleRemovePlant = (plantId: string, event: React.MouseEvent) => {
    event.preventDefault(); // Prevent navigation to plant detail
    event.stopPropagation();
    setShowRemoveConfirm(plantId);
  };

  // Confirm plant removal
  const confirmRemovePlant = (plantId: string) => {
    if (isCustomPlant(plantId)) {
      // Permanently delete custom plants
      deleteCustomPlant(plantId);
    } else {
      // Hide default plants
      removePlant(plantId);
    }

    // Refresh the plants list
    setPlants(getAllManagedPlants());
    setShowRemoveConfirm(null);
  };

  // Cancel removal
  const cancelRemove = () => {
    setShowRemoveConfirm(null);
  };

  const getDifficultyColor = (difficulty?: string) => {
    switch (difficulty) {
      case "Easy": return "bg-green-100 text-green-800";
      case "Moderate": return "bg-yellow-100 text-yellow-800";
      case "Difficult": return "bg-red-100 text-red-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <header className="bg-emerald-600 text-white p-4 shadow-md">
        <div className="container mx-auto flex justify-between items-center">
          <Link to="/" className="text-2xl font-bold">GardenPal</Link>
          <Link
            to="/identify"
            className="bg-white text-emerald-600 hover:bg-gray-100 font-bold py-1 px-3 rounded-lg text-sm transition-colors"
          >
            Identify Plant
          </Link>
        </div>
      </header>

      <main className="flex-grow container mx-auto p-3 sm:p-4 md:p-6">
        <div className="max-w-6xl mx-auto">
          <div className="mb-6 sm:mb-8">
            <h1 className="text-2xl sm:text-3xl font-bold mb-3 sm:mb-4">My Plant Library</h1>
            <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300 mb-4 sm:mb-6">
              Discover and learn about different plants. Click on any plant to view detailed care information.
            </p>

            {/* Search and Filter Controls */}
            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4 mb-4 sm:mb-6">
              <div className="flex-1">
                <input
                  type="text"
                  placeholder="Search plants..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full px-3 sm:px-4 py-2 text-sm sm:text-base border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 dark:bg-gray-800 dark:border-gray-600 dark:text-white"
                />
              </div>

              <div className="flex gap-1 sm:gap-2 overflow-x-auto">
                <button
                  onClick={() => setFilter("all")}
                  className={`px-3 sm:px-4 py-2 rounded-lg font-medium transition-colors text-xs sm:text-sm whitespace-nowrap ${
                    filter === "all"
                      ? "bg-emerald-600 text-white"
                      : "bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
                  }`}
                >
                  All
                </button>
                <button
                  onClick={() => setFilter("easy")}
                  className={`px-3 sm:px-4 py-2 rounded-lg font-medium transition-colors text-xs sm:text-sm whitespace-nowrap ${
                    filter === "easy"
                      ? "bg-emerald-600 text-white"
                      : "bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
                  }`}
                >
                  Easy
                </button>
                <button
                  onClick={() => setFilter("moderate")}
                  className={`px-3 sm:px-4 py-2 rounded-lg font-medium transition-colors text-xs sm:text-sm whitespace-nowrap ${
                    filter === "moderate"
                      ? "bg-emerald-600 text-white"
                      : "bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
                  }`}
                >
                  Moderate
                </button>
                <button
                  onClick={() => setFilter("difficult")}
                  className={`px-3 sm:px-4 py-2 rounded-lg font-medium transition-colors text-xs sm:text-sm whitespace-nowrap ${
                    filter === "difficult"
                      ? "bg-emerald-600 text-white"
                      : "bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600"
                  }`}
                >
                  Difficult
                </button>
              </div>
            </div>
          </div>

          {/* Plants Grid */}
          {filteredPlants.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">No plants found</h3>
              <p className="text-gray-500 dark:text-gray-400">Try adjusting your search or filter criteria.</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredPlants.map((plant) => {
                return (
                  <div
                    key={plant.id}
                    className="group bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow overflow-hidden relative"
                  >
                    {/* Remove Button */}
                    <button
                      onClick={(e) => handleRemovePlant(plant.id, e)}
                      className="absolute top-2 right-2 z-10 bg-red-500 hover:bg-red-600 text-white rounded-full p-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 shadow-lg"
                      title="Remove plant from library"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H8a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </button>

                    <Link
                      to={`/result/${plant.id}`}
                      className="block"
                    >
                      <div className="aspect-w-16 aspect-h-12 bg-gray-200 dark:bg-gray-700">
                        <img
                          src={plant.image}
                          alt={plant.name}
                          className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                        />
                      </div>
                      <div className="p-4">
                        <div className="flex items-start justify-between mb-2">
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 group-hover:text-emerald-600 transition-colors">
                            {plant.name}
                          </h3>
                          {plant.difficulty && (
                            <span className={`text-xs px-2 py-1 rounded-full ${getDifficultyColor(plant.difficulty)}`}>
                              {plant.difficulty}
                            </span>
                          )}
                        </div>
                        <p className="text-sm text-gray-500 dark:text-gray-400 italic mb-2">
                          {plant.scientificName}
                        </p>
                        <p className="text-sm text-gray-600 dark:text-gray-300 line-clamp-2">
                          {plant.description}
                        </p>
                        <div className="mt-3 flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                          <span>{plant.family}</span>
                          {plant.size && <span>{plant.size}</span>}
                        </div>
                      </div>
                    </Link>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </main>

      {/* Remove Confirmation Modal */}
      {showRemoveConfirm && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full p-6">
            <div className="flex items-center mb-4">
              <div className="flex-shrink-0">
                <svg className="h-6 w-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  Remove Plant
                </h3>
              </div>
            </div>

            <div className="mb-6">
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {isCustomPlant(showRemoveConfirm)
                  ? "This will permanently delete this plant from your library. This action cannot be undone."
                  : "This will remove this plant from your library. You can add it back later if needed."
                }
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-3 sm:gap-4">
              <button
                onClick={cancelRemove}
                className="flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-200 hover:bg-gray-300 rounded-lg transition-colors dark:bg-gray-600 dark:text-gray-300 dark:hover:bg-gray-500"
              >
                Cancel
              </button>
              <button
                onClick={() => confirmRemovePlant(showRemoveConfirm)}
                className="flex-1 px-4 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-lg transition-colors"
              >
                {isCustomPlant(showRemoveConfirm) ? "Delete" : "Remove"}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
