import { useState, useRef } from "react";
import { Link } from "@remix-run/react";
import { MetaFunction } from "@remix-run/node";
import { 
  exportPlantLibrary, 
  importPlantLibrary, 
  downloadPlantLibrary, 
  getLibraryStats,
  PlantLibraryExport 
} from "../utils/plantManager";

export const meta: MetaFunction = () => {
  return [
    { title: "Sync Library - PlantPal" },
    { name: "description", content: "Sync your plant library between devices" },
  ];
};

export default function Sync() {
  const [stats, setStats] = useState(getLibraryStats());
  const [importStatus, setImportStatus] = useState<string>("");
  const [showImportOptions, setShowImportOptions] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleExport = () => {
    try {
      downloadPlantLibrary();
      setImportStatus("✅ Library exported successfully! Check your downloads folder.");
    } catch (error) {
      setImportStatus("❌ Failed to export library. Please try again.");
    }
  };

  const handleImportFile = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const data: PlantLibraryExport = JSON.parse(e.target?.result as string);
        
        // Validate the data structure
        if (!data.customPlants || !Array.isArray(data.removedPlantIds)) {
          throw new Error("Invalid file format");
        }

        setShowImportOptions(true);
        setImportStatus(`📁 File loaded: ${Object.keys(data.customPlants).length} plants found. Choose import mode below.`);
        
        // Store data temporarily for import
        (window as any).pendingImportData = data;
      } catch (error) {
        setImportStatus("❌ Invalid file format. Please select a valid PlantPal library file.");
      }
    };
    reader.readAsText(file);
  };

  const handleImport = (mode: 'replace' | 'merge') => {
    const data = (window as any).pendingImportData;
    if (!data) {
      setImportStatus("❌ No file data found. Please select a file first.");
      return;
    }

    try {
      const success = importPlantLibrary(data, mode);
      if (success) {
        setStats(getLibraryStats());
        setImportStatus(`✅ Library ${mode === 'replace' ? 'replaced' : 'merged'} successfully!`);
        setShowImportOptions(false);
        delete (window as any).pendingImportData;
        
        // Clear file input
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
      } else {
        setImportStatus("❌ Failed to import library. Please try again.");
      }
    } catch (error) {
      setImportStatus("❌ Import failed. Please check the file and try again.");
    }
  };

  const copyToClipboard = async () => {
    try {
      const data = exportPlantLibrary();
      await navigator.clipboard.writeText(JSON.stringify(data, null, 2));
      setImportStatus("✅ Library data copied to clipboard! You can paste this on another device.");
    } catch (error) {
      setImportStatus("❌ Failed to copy to clipboard. Try the download option instead.");
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <header className="bg-emerald-600 text-white p-4 shadow-md">
        <div className="container mx-auto flex justify-between items-center">
          <Link to="/" className="text-2xl font-bold">PlantPal</Link>
          <Link to="/library" className="text-emerald-100 hover:text-white">
            Back to Library
          </Link>
        </div>
      </header>

      <main className="container mx-auto p-4 md:p-6">
        <div className="max-w-2xl mx-auto">
          <h1 className="text-3xl font-bold mb-6 text-gray-900 dark:text-white">
            Sync Plant Library
          </h1>
          
          <p className="text-gray-600 dark:text-gray-300 mb-8">
            Your plant library is stored locally on each device. Use these tools to sync your plants between your Mac, iPhone, and other devices.
          </p>

          {/* Library Statistics */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
              Current Library
            </h2>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-emerald-600">{stats.totalPlants}</div>
                <div className="text-sm text-gray-600 dark:text-gray-300">Total Plants</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{stats.identifiedPlants}</div>
                <div className="text-sm text-gray-600 dark:text-gray-300">Identified Plants</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">{stats.customPlants}</div>
                <div className="text-sm text-gray-600 dark:text-gray-300">Saved Plants</div>
              </div>
            </div>
          </div>

          {/* Export Section */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white flex items-center">
              <svg className="h-6 w-6 text-emerald-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
              </svg>
              Export Library
            </h2>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Save your plant library to transfer to another device.
            </p>
            <div className="flex flex-col sm:flex-row gap-3">
              <button
                onClick={handleExport}
                className="flex-1 bg-emerald-600 text-white px-4 py-2 rounded-lg hover:bg-emerald-700 transition-colors"
              >
                Download File
              </button>
              <button
                onClick={copyToClipboard}
                className="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Copy to Clipboard
              </button>
            </div>
          </div>

          {/* Import Section */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white flex items-center">
              <svg className="h-6 w-6 text-blue-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
              </svg>
              Import Library
            </h2>
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Load a plant library from another device.
            </p>
            
            <input
              ref={fileInputRef}
              type="file"
              accept=".json"
              onChange={handleImportFile}
              className="block w-full text-sm text-gray-500 dark:text-gray-300
                file:mr-4 file:py-2 file:px-4
                file:rounded-lg file:border-0
                file:text-sm file:font-medium
                file:bg-emerald-50 file:text-emerald-700
                hover:file:bg-emerald-100
                dark:file:bg-emerald-900 dark:file:text-emerald-300"
            />

            {showImportOptions && (
              <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <h3 className="font-medium text-gray-900 dark:text-white mb-3">Import Mode:</h3>
                <div className="flex flex-col sm:flex-row gap-3">
                  <button
                    onClick={() => handleImport('merge')}
                    className="flex-1 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors"
                  >
                    Merge (Keep Both)
                  </button>
                  <button
                    onClick={() => handleImport('replace')}
                    className="flex-1 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
                  >
                    Replace (Overwrite)
                  </button>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-300 mt-2">
                  <strong>Merge:</strong> Combines libraries, keeping plants from both devices.<br/>
                  <strong>Replace:</strong> Replaces your current library with the imported one.
                </p>
              </div>
            )}
          </div>

          {/* Status Messages */}
          {importStatus && (
            <div className="bg-gray-100 dark:bg-gray-700 rounded-lg p-4 mb-6">
              <p className="text-gray-800 dark:text-gray-200">{importStatus}</p>
            </div>
          )}

          {/* Instructions */}
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
            <h3 className="font-semibold text-blue-800 dark:text-blue-200 mb-3">
              How to Sync Between Devices:
            </h3>
            <ol className="list-decimal list-inside space-y-2 text-blue-700 dark:text-blue-300 text-sm">
              <li>On your first device (e.g., Mac): Click "Download File" or "Copy to Clipboard"</li>
              <li>Transfer the file to your second device (e.g., iPhone) via AirDrop, email, or cloud storage</li>
              <li>On your second device: Open PlantPal, go to Sync, and import the file</li>
              <li>Choose "Merge" to combine libraries or "Replace" to overwrite</li>
              <li>Your plant libraries are now synced!</li>
            </ol>
          </div>
        </div>
      </main>
    </div>
  );
}
