import { Link } from "@remix-run/react";
import { useState } from "react";
import { MetaFunction } from "@remix-run/node";

export const meta: MetaFunction = () => {
  return [
    { title: "PlantPal - Plant Care Assistant" },
    { name: "description", content: "Your personal gardening assistant" },
  ];
};

export default function Index() {
  return (
    <div className="min-h-screen flex flex-col">
      <header className="bg-emerald-600 text-white p-4 shadow-md">
        <div className="container mx-auto flex justify-between items-center">
          <h1 className="text-2xl font-bold">PlantPal</h1>
        </div>
      </header>

      <main className="flex-grow container mx-auto p-4 md:p-6">
        <section className="max-w-md mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-8">
          <h2 className="text-xl font-bold mb-4 text-center">Welcome to PlantPal</h2>
          <p className="text-gray-600 dark:text-gray-300 mb-6 text-center">
            Your personal plant identification and care assistant
          </p>

          <div className="space-y-4">
            <Link
              to="/identify"
              className="block w-full bg-emerald-600 hover:bg-emerald-700 text-white font-bold py-3 px-4 rounded-lg text-center transition-colors"
            >
              Identify a Plant
            </Link>

            <Link
              to="/library"
              className="block w-full bg-emerald-100 hover:bg-emerald-200 text-emerald-800 font-bold py-3 px-4 rounded-lg text-center transition-colors"
            >
              My Plant Library
            </Link>

            <Link
              to="/soil-calculator"
              className="block w-full bg-amber-100 hover:bg-amber-200 text-amber-800 font-bold py-3 px-4 rounded-lg text-center transition-colors"
            >
              Garden Bed Builder
            </Link>
          </div>
        </section>

        <section className="max-w-md mx-auto">
          <h3 className="text-lg font-semibold mb-3">Features</h3>
          <ul className="space-y-2">
            <li className="flex items-start">
              <span className="text-emerald-600 mr-2">✓</span>
              <span>Identify plants from photos</span>
            </li>
            <li className="flex items-start">
              <span className="text-emerald-600 mr-2">✓</span>
              <span>Get personalized care information</span>
            </li>
            <li className="flex items-start">
              <span className="text-emerald-600 mr-2">✓</span>
              <span>Build layered garden beds with natural materials</span>
            </li>
            <li className="flex items-start">
              <span className="text-emerald-600 mr-2">✓</span>
              <span>Generate downloadable plant care cards</span>
            </li>
            <li className="flex items-start">
              <span className="text-emerald-600 mr-2">✓</span>
              <span>Works offline as a Progressive Web App</span>
            </li>
          </ul>
        </section>
      </main>

      <footer className="bg-gray-100 dark:bg-gray-800 p-4 text-center text-sm text-gray-600 dark:text-gray-400">
        <p>PlantPal © {new Date().getFullYear()}</p>
      </footer>
    </div>
  );
}
