import { json, type ActionFunctionArgs } from "@remix-run/node";

export async function action({ request }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return json({ error: "Method not allowed" }, { status: 405 });
  }

  try {
    const { message } = await request.json();

    if (!message || typeof message !== "string") {
      return json({ error: "Message is required" }, { status: 400 });
    }

    // Check if OpenAI API key is configured
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey || apiKey === "your_openai_api_key_here") {
      return json({ 
        error: "AI chat is not configured. Please add your OpenAI API key to enable this feature.",
        needsConfig: true 
      }, { status: 503 });
    }

    // Call OpenAI API for plant-related chat
    const response = await fetch("https://api.openai.com/v1/chat/completions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bear<PERSON> ${apiKey}`,
      },
      body: JSON.stringify({
        model: "gpt-3.5-turbo",
        messages: [
          {
            role: "system",
            content: `You are PlantPal AI, a friendly and knowledgeable plant expert assistant. You help users with:

- Plant identification and care advice
- Troubleshooting plant problems
- Watering, lighting, and fertilizing guidance
- Soil mix recommendations
- Plant propagation tips
- Pest and disease identification
- Indoor gardening advice
- Plant selection for specific conditions

Always be helpful, encouraging, and specific in your advice. If you're not sure about something, say so and suggest consulting a local expert. Keep responses concise but informative. Use a friendly, conversational tone.

The user has access to these materials for soil mixes:
- Old soil, Compost, Coconut husk chunks, Coconut coir, Small sticks, Fallen coconut fronds, Dried leaves, Liquid microbial fertilizer, Fish emulsion

When suggesting soil mixes, use these materials and provide measurements in 20L buckets and tablespoons for fertilizers.`
          },
          {
            role: "user",
            content: message
          }
        ],
        max_tokens: 500,
        temperature: 0.7,
      }),
    });

    if (!response.ok) {
      const error = await response.text();
      console.error("OpenAI API error:", response.status, error);
      return json({ 
        error: "AI service temporarily unavailable. Please try again later." 
      }, { status: 503 });
    }

    const data = await response.json();
    const aiResponse = data.choices[0]?.message?.content || "I'm sorry, I couldn't generate a response. Please try again.";

    return json({ 
      success: true, 
      response: aiResponse 
    });

  } catch (error) {
    console.error("Plant chat error:", error);
    return json({ 
      error: "An error occurred while processing your message. Please try again." 
    }, { status: 500 });
  }
}
