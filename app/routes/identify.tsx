import { useState, useRef, useEffect } from "react";
import { Link, useNavigate } from "@remix-run/react";
import { MetaFunction } from "@remix-run/node";

export const meta: MetaFunction = () => {
  return [
    { title: "Identify Plant - GardenPal" },
    { name: "description", content: "Identify your plants with GardenPal" },
  ];
};

export default function Identify() {
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const navigate = useNavigate();

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Reset states
    setError(null);

    // Check file type
    if (!file.type.startsWith('image/')) {
      setError('Please select an image file');
      return;
    }

    // Check file size (limit to 10MB)
    if (file.size > 10 * 1024 * 1024) {
      setError('Image size should be less than 10MB');
      return;
    }

    const reader = new FileReader();
    reader.onload = () => {
      setImagePreview(reader.result as string);
    };
    reader.readAsDataURL(file);
  };

  const handleCapture = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleIdentify = async () => {
    if (!imagePreview) {
      setError('Please select or capture an image first');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // Send image to our identification API
      const formData = new FormData();
      formData.append('image', imagePreview);

      const response = await fetch('/api/plant-identify', {
        method: 'POST',
        body: formData,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to identify plant');
      }

      if (data.success && data.result) {
        // Store the identification result in sessionStorage for the result page
        sessionStorage.setItem('plantIdentificationResult', JSON.stringify(data.result));
        sessionStorage.setItem('plantIdentificationLowConfidence', data.lowConfidence ? 'true' : 'false');

        // Navigate to the new Plant.id result page
        navigate('/plant-result');
      } else {
        throw new Error(data.error || 'No identification result received');
      }
    } catch (error) {
      console.error('Identification error:', error);
      setError(error instanceof Error ? error.message : 'Failed to identify plant. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <header className="bg-emerald-600 text-white p-4 shadow-md">
        <div className="container mx-auto flex justify-between items-center">
          <Link to="/" className="text-2xl font-bold">GardenPal</Link>
        </div>
      </header>

      <main className="flex-grow container mx-auto p-4 md:p-6">
        <div className="max-w-md mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h1 className="text-xl font-bold mb-4">Identify Your Plant</h1>

          <div className="mb-6">
            <p className="text-gray-600 dark:text-gray-300 mb-4">
              Take a photo or upload an image of your plant to identify it and get care information.
            </p>

            <div className="flex justify-center space-x-4 mb-6">
              <button
                onClick={handleCapture}
                className="bg-emerald-600 hover:bg-emerald-700 text-white font-bold py-2 px-4 rounded-lg transition-colors flex items-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M4 5a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V7a2 2 0 00-2-2h-1.586a1 1 0 01-.707-.293l-1.121-1.121A2 2 0 0011.172 3H8.828a2 2 0 00-1.414.586L6.293 4.707A1 1 0 015.586 5H4zm6 9a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                </svg>
                Take Photo
              </button>

              <input
                type="file"
                accept="image/*"
                onChange={handleFileChange}
                ref={fileInputRef}
                className="hidden"
                capture="environment"
              />

              <button
                onClick={() => fileInputRef.current?.click()}
                className="bg-gray-200 hover:bg-gray-300 dark:bg-gray-700 dark:hover:bg-gray-600 text-gray-800 dark:text-white font-bold py-2 px-4 rounded-lg transition-colors flex items-center"
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8 4a3 3 0 00-3 3v4a5 5 0 0010 0V7a3 3 0 00-3-3 3 3 0 00-3 3v4a3 3 0 006 0V7a1 1 0 112 0v4a5 5 0 01-10 0V7a3 3 0 013-3z" clipRule="evenodd" />
                </svg>
                Upload
              </button>
            </div>

            {error && (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                {error}
              </div>
            )}

            {imagePreview && (
              <div className="mb-4">
                <div className="relative rounded-lg overflow-hidden bg-gray-100 dark:bg-gray-700 aspect-square">
                  <img
                    src={imagePreview}
                    alt="Plant preview"
                    className="w-full h-full object-contain"
                  />
                </div>
              </div>
            )}

            <button
              onClick={handleIdentify}
              disabled={!imagePreview || isLoading}
              className={`w-full font-bold py-3 px-4 rounded-lg transition-colors flex items-center justify-center ${
                !imagePreview || isLoading
                  ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  : 'bg-emerald-600 hover:bg-emerald-700 text-white'
              }`}
            >
              {isLoading ? (
                <>
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Identifying...
                </>
              ) : (
                'Identify Plant'
              )}
            </button>
          </div>

          <div className="text-sm text-gray-500 dark:text-gray-400">
            <p>Tips for better identification:</p>
            <ul className="list-disc pl-5 mt-1 space-y-1">
              <li>Take a clear, well-lit photo</li>
              <li>Focus on leaves and any flowers</li>
              <li>Avoid blurry or dark images</li>
            </ul>
          </div>
        </div>
      </main>
    </div>
  );
}
