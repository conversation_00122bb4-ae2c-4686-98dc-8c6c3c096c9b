import { useState } from "react";
import { Link } from "@remix-run/react";
import { MetaFunction } from "@remix-run/node";
import { ErrorBoundary } from "../components/ErrorBoundary";

export const meta: MetaFunction = () => {
  return [
    { title: "Garden Bed Builder - GardenPal" },
    { name: "description", content: "Create custom soil mixes and build layered garden beds using natural materials" },
  ];
};

export { ErrorBoundary };

interface SoilComponent {
  name: string;
  description: string;
  benefits: string[];
  percentage: number;
}

interface SoilMix {
  name: string;
  description: string;
  components: SoilComponent[];
  bestFor: string[];
}

const soilComponents: Record<string, Omit<SoilComponent, 'percentage'>> = {
  compost: {
    name: "Compost",
    description: "Rich organic matter with nutrients",
    benefits: ["High in nutrients", "Improves soil structure", "Retains moisture", "Feeds beneficial microbes"]
  },
  topSoil: {
    name: "Top Soil",
    description: "Quality garden soil base",
    benefits: ["Good foundation", "Natural nutrients", "Plant-ready structure"]
  },
  coconutCoir: {
    name: "Coconut Coir",
    description: "Processed coconut fiber",
    benefits: ["Excellent water retention", "pH neutral", "Sustainable", "Improves aeration"]
  },
  coconutHusk: {
    name: "Coconut Husk (Chunks)",
    description: "Chunky coconut husk pieces",
    benefits: ["Excellent drainage", "Long-lasting structure", "Prevents compaction", "Natural and sustainable"]
  },
  coconutFrondsWhole: {
    name: "Coconut Fronds (Whole)",
    description: "Large coconut palm fronds",
    benefits: ["Bottom layer drainage", "Slow decomposition", "Creates air pockets", "Natural mulch"]
  },
  coconutFrondsShredded: {
    name: "Coconut Fronds (Shredded)",
    description: "Chopped coconut palm fronds",
    benefits: ["Good mulch layer", "Moisture retention", "Weed suppression", "Gradual nutrient release"]
  },
  dryLeaves: {
    name: "Dry Leaves",
    description: "Fallen and dried leaves",
    benefits: ["Natural mulch", "Moisture retention", "Adds organic matter", "Weed suppression"]
  },
  bigSticks: {
    name: "Big Sticks/Branches",
    description: "Large woody material",
    benefits: ["Bottom drainage layer", "Air circulation", "Slow decomposition", "Structural support"]
  },
  smallSticks: {
    name: "Small Sticks/Twigs",
    description: "Small woody debris",
    benefits: ["Improves drainage", "Adds structure", "Gradual organic matter", "Prevents compaction"]
  }
};

const presetMixes: SoilMix[] = [
  {
    name: "Rich Garden Bed Mix",
    description: "Nutrient-rich mix for vegetables and flowering plants",
    components: [
      { ...soilComponents.topSoil, percentage: 40 },
      { ...soilComponents.compost, percentage: 30 },
      { ...soilComponents.coconutCoir, percentage: 20 },
      { ...soilComponents.smallSticks, percentage: 10 }
    ],
    bestFor: ["Vegetables", "Herbs", "Flowering plants", "Fruit trees"]
  },
  {
    name: "Well-Draining Garden Mix",
    description: "Perfect for plants that don't like wet feet",
    components: [
      { ...soilComponents.topSoil, percentage: 35 },
      { ...soilComponents.compost, percentage: 25 },
      { ...soilComponents.coconutHusk, percentage: 25 },
      { ...soilComponents.smallSticks, percentage: 15 }
    ],
    bestFor: ["Mediterranean herbs", "Lavender", "Rosemary", "Succulents"]
  },
  {
    name: "Moisture-Retaining Mix",
    description: "Holds water well for thirsty plants",
    components: [
      { ...soilComponents.topSoil, percentage: 35 },
      { ...soilComponents.compost, percentage: 35 },
      { ...soilComponents.coconutCoir, percentage: 30 }
    ],
    bestFor: ["Leafy greens", "Ferns", "Tropical plants", "Water-loving vegetables"]
  }
];

const gardenBedLayers = [
  {
    name: "Bottom Drainage Layer",
    description: "Foundation layer for drainage and aeration",
    materials: ["Big Sticks/Branches", "Coconut Fronds (Whole)"],
    thickness: "4-6 inches",
    purpose: "Prevents waterlogging and creates air pockets"
  },
  {
    name: "Coarse Organic Layer",
    description: "Transition layer with medium-sized organic matter",
    materials: ["Small Sticks/Twigs", "Coconut Husk (Chunks)", "Coconut Fronds (Shredded)"],
    thickness: "2-3 inches",
    purpose: "Improves drainage while starting to provide nutrients"
  },
  {
    name: "Main Growing Medium",
    description: "Primary soil mix where plants will root",
    materials: ["Top Soil", "Compost", "Coconut Coir"],
    thickness: "8-12 inches",
    purpose: "Provides nutrients, structure, and growing space for roots"
  },
  {
    name: "Top Mulch Layer",
    description: "Surface protection and moisture retention",
    materials: ["Dry Leaves", "Coconut Fronds (Shredded)"],
    thickness: "2-3 inches",
    purpose: "Retains moisture, suppresses weeds, and slowly adds nutrients"
  }
];

export default function SoilCalculator() {
  const [selectedPreset, setSelectedPreset] = useState<SoilMix | null>(null);
  const [customMix, setCustomMix] = useState<SoilComponent[]>([
    { ...soilComponents.topSoil, percentage: 40 },
    { ...soilComponents.compost, percentage: 30 },
    { ...soilComponents.coconutCoir, percentage: 20 },
    { ...soilComponents.smallSticks, percentage: 10 }
  ]);
  const [potSize, setPotSize] = useState<number>(6);
  const [activeTab, setActiveTab] = useState<"presets" | "custom" | "layers">("presets");

  const calculateVolume = (diameter: number): number => {
    // Simplified calculation: assume pot height ≈ diameter
    const radius = diameter / 2;
    const height = diameter * 0.8; // Typical pot proportions
    return Math.PI * radius * radius * height;
  };

  const calculateAmounts = (mix: SoilComponent[], volume: number) => {
    return mix.map(component => ({
      ...component,
      amount: Math.round((component.percentage / 100) * volume * 10) / 10
    }));
  };

  const updateCustomPercentage = (index: number, percentage: number) => {
    const newMix = [...customMix];
    newMix[index].percentage = percentage;
    setCustomMix(newMix);
  };

  const addCustomComponent = (componentKey: string) => {
    const component = soilComponents[componentKey];
    if (component && !customMix.find(c => c.name === component.name)) {
      setCustomMix([...customMix, { ...component, percentage: 10 }]);
    }
  };

  const removeCustomComponent = (index: number) => {
    if (customMix.length > 1) {
      const newMix = customMix.filter((_, i) => i !== index);
      setCustomMix(newMix);
    }
  };

  const totalPercentage = customMix.reduce((sum, component) => sum + component.percentage, 0);
  const volume = calculateVolume(potSize);
  const currentMix = selectedPreset ? selectedPreset.components : customMix;
  const amounts = calculateAmounts(currentMix, volume);

  return (
    <div className="min-h-screen flex flex-col">
      <header className="bg-emerald-600 text-white p-4 shadow-md">
        <div className="container mx-auto flex justify-between items-center">
          <Link to="/" className="text-2xl font-bold">GardenPal</Link>
          <Link
            to="/library"
            className="bg-white text-emerald-600 hover:bg-gray-100 font-bold py-1 px-3 rounded-lg text-sm transition-colors"
          >
            Plant Library
          </Link>
        </div>
      </header>

      <main className="flex-grow container mx-auto p-4 md:p-6">
        <div className="max-w-4xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-4">Garden Bed Builder</h1>
            <p className="text-gray-600 dark:text-gray-300">
              Create custom soil mixes and build layered garden beds using your available natural materials.
              Perfect for sustainable gardening with coconut-based materials and organic matter.
            </p>
          </div>

          {/* Garden Bed Size Input */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">Garden Bed Size</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Length (feet):</label>
                <input
                  type="number"
                  min="1"
                  max="50"
                  value={Math.round(potSize * 0.5)}
                  onChange={(e) => setPotSize(Number(e.target.value) * 2)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Width (feet):</label>
                <input
                  type="number"
                  min="1"
                  max="20"
                  value={Math.round(potSize * 0.3)}
                  onChange={(e) => setPotSize(Number(e.target.value) * 3.33)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Depth (inches):</label>
                <input
                  type="number"
                  min="6"
                  max="24"
                  value={Math.round(potSize * 2)}
                  onChange={(e) => setPotSize(Number(e.target.value) * 0.5)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>
            </div>
            <div className="mt-4 p-3 bg-emerald-50 dark:bg-emerald-900/20 rounded-lg">
              <span className="text-sm text-emerald-700 dark:text-emerald-300">
                Total volume needed: ≈ {Math.round(volume * 0.0058)} cubic feet of materials
              </span>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden mb-6">
            <div className="flex border-b border-gray-200 dark:border-gray-700">
              <button
                className={`flex-1 py-3 px-4 text-center font-medium ${
                  activeTab === "presets"
                    ? "text-emerald-600 border-b-2 border-emerald-600 bg-emerald-50 dark:bg-emerald-900/20"
                    : "text-gray-500 hover:text-emerald-600"
                }`}
                onClick={() => {
                  setActiveTab("presets");
                  setSelectedPreset(null);
                }}
              >
                Soil Mixes
              </button>
              <button
                className={`flex-1 py-3 px-4 text-center font-medium ${
                  activeTab === "custom"
                    ? "text-emerald-600 border-b-2 border-emerald-600 bg-emerald-50 dark:bg-emerald-900/20"
                    : "text-gray-500 hover:text-emerald-600"
                }`}
                onClick={() => {
                  setActiveTab("custom");
                  setSelectedPreset(null);
                }}
              >
                Custom Mix
              </button>
              <button
                className={`flex-1 py-3 px-4 text-center font-medium ${
                  activeTab === "layers"
                    ? "text-emerald-600 border-b-2 border-emerald-600 bg-emerald-50 dark:bg-emerald-900/20"
                    : "text-gray-500 hover:text-emerald-600"
                }`}
                onClick={() => {
                  setActiveTab("layers");
                  setSelectedPreset(null);
                }}
              >
                Garden Bed Layers
              </button>
            </div>

            <div className="p-6">
              {activeTab === "presets" && (
                <div className="space-y-4">
                  {presetMixes.map((mix, index) => (
                    <div
                      key={index}
                      className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                        selectedPreset === mix
                          ? "border-emerald-500 bg-emerald-50 dark:bg-emerald-900/20"
                          : "border-gray-200 dark:border-gray-600 hover:border-emerald-300"
                      }`}
                      onClick={() => setSelectedPreset(mix)}
                    >
                      <h3 className="font-semibold text-lg mb-2">{mix.name}</h3>
                      <p className="text-gray-600 dark:text-gray-300 text-sm mb-3">{mix.description}</p>
                      <div className="flex flex-wrap gap-2 mb-3">
                        {mix.bestFor.map((plant, plantIndex) => (
                          <span
                            key={plantIndex}
                            className="bg-emerald-100 text-emerald-800 text-xs px-2 py-1 rounded-full"
                          >
                            {plant}
                          </span>
                        ))}
                      </div>
                      <div className="text-sm">
                        <strong>Components:</strong> {mix.components.map(c => `${c.name} (${c.percentage}%)`).join(", ")}
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {activeTab === "custom" && (
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <h3 className="font-semibold">Custom Mix Components</h3>
                    <span className={`text-sm ${totalPercentage === 100 ? 'text-green-600' : 'text-red-600'}`}>
                      Total: {totalPercentage}%
                    </span>
                  </div>

                  {customMix.map((component, index) => (
                    <div key={index} className="flex items-center space-x-4 p-3 border border-gray-200 dark:border-gray-600 rounded-lg">
                      <div className="flex-1">
                        <div className="font-medium">{component.name}</div>
                        <div className="text-sm text-gray-500">{component.description}</div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="number"
                          min="0"
                          max="100"
                          value={component.percentage}
                          onChange={(e) => updateCustomPercentage(index, Number(e.target.value))}
                          className="w-16 px-2 py-1 border border-gray-300 rounded focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                        />
                        <span className="text-sm">%</span>
                        {customMix.length > 1 && (
                          <button
                            onClick={() => removeCustomComponent(index)}
                            className="text-red-500 hover:text-red-700 p-1"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                          </button>
                        )}
                      </div>
                    </div>
                  ))}

                  <div className="border-t pt-4">
                    <h4 className="font-medium mb-2">Add Component:</h4>
                    <div className="flex flex-wrap gap-2">
                      {Object.entries(soilComponents).map(([key, component]) => (
                        <button
                          key={key}
                          onClick={() => addCustomComponent(key)}
                          disabled={customMix.find(c => c.name === component.name) !== undefined}
                          className="px-3 py-1 text-sm border border-gray-300 rounded-full hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:border-gray-600 dark:hover:bg-gray-700"
                        >
                          {component.name}
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              )}

              {activeTab === "layers" && (
                <div className="space-y-6">
                  <div className="mb-6">
                    <h3 className="text-lg font-semibold mb-3">Garden Bed Construction Guide</h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
                      Build your garden bed from bottom to top using your available materials. This layered approach
                      creates excellent drainage, provides nutrients, and mimics natural forest floor composition.
                    </p>
                  </div>

                  {gardenBedLayers.map((layer, index) => (
                    <div key={index} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div>
                          <h4 className="font-semibold text-lg">{layer.name}</h4>
                          <p className="text-sm text-gray-600 dark:text-gray-300">{layer.description}</p>
                        </div>
                        <div className="text-right">
                          <span className="bg-emerald-100 text-emerald-800 text-xs px-2 py-1 rounded-full">
                            {layer.thickness}
                          </span>
                        </div>
                      </div>

                      <div className="mb-3">
                        <h5 className="font-medium text-sm mb-2">Materials to use:</h5>
                        <div className="flex flex-wrap gap-2">
                          {layer.materials.map((material, materialIndex) => (
                            <span
                              key={materialIndex}
                              className="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full"
                            >
                              {material}
                            </span>
                          ))}
                        </div>
                      </div>

                      <div className="text-sm text-gray-600 dark:text-gray-300">
                        <strong>Purpose:</strong> {layer.purpose}
                      </div>
                    </div>
                  ))}

                  <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4">
                    <h4 className="font-medium text-amber-800 dark:text-amber-300 mb-2">💡 Pro Tips:</h4>
                    <ul className="text-sm text-amber-700 dark:text-amber-300 space-y-1">
                      <li>• Water each layer lightly as you build to help materials settle</li>
                      <li>• Break down large coconut fronds into smaller pieces for better layering</li>
                      <li>• Mix different sizes of sticks in the bottom layer for better drainage</li>
                      <li>• Refresh the top mulch layer every 3-4 months</li>
                      <li>• Add more compost to the growing medium layer seasonally</li>
                    </ul>
                  </div>

                  <div className="bg-emerald-50 dark:bg-emerald-900/20 border border-emerald-200 dark:border-emerald-800 rounded-lg p-4">
                    <h4 className="font-medium text-emerald-800 dark:text-emerald-300 mb-2">🌱 Seasonal Maintenance:</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-emerald-700 dark:text-emerald-300">
                      <div>
                        <strong>Spring/Summer:</strong>
                        <ul className="mt-1 space-y-1">
                          <li>• Add fresh compost layer</li>
                          <li>• Refresh mulch as needed</li>
                          <li>• Check drainage after heavy rains</li>
                        </ul>
                      </div>
                      <div>
                        <strong>Fall/Winter:</strong>
                        <ul className="mt-1 space-y-1">
                          <li>• Add fallen leaves to mulch layer</li>
                          <li>• Collect and prepare new organic materials</li>
                          <li>• Plan for next season's improvements</li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Results */}
          {(selectedPreset || activeTab === "custom") && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4">
                {selectedPreset ? selectedPreset.name : "Custom Mix"} - Shopping List
              </h2>

              {activeTab === "custom" && totalPercentage !== 100 && (
                <div className="bg-yellow-50 border border-yellow-200 text-yellow-800 px-4 py-3 rounded-lg mb-4">
                  <strong>Warning:</strong> Your mix percentages don't add up to 100%.
                  Adjust the percentages for accurate measurements.
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {amounts.map((component, index) => (
                  <div key={index} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="font-medium">{component.name}</h3>
                      <span className="text-lg font-bold text-emerald-600">
                        {component.amount} cu in
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                      {component.description}
                    </p>
                    <div className="text-xs text-gray-500">
                      {component.percentage}% of total mix
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-6 p-4 bg-emerald-50 dark:bg-emerald-900/20 rounded-lg">
                <h3 className="font-medium text-emerald-800 dark:text-emerald-300 mb-2">Garden Bed Preparation:</h3>
                <ol className="text-sm text-emerald-700 dark:text-emerald-300 space-y-1">
                  <li>1. Gather all components in the amounts listed above</li>
                  <li>2. Start with drainage materials (big sticks, coconut fronds) at the bottom</li>
                  <li>3. Add coarse organic matter (small sticks, coconut husk chunks)</li>
                  <li>4. Mix soil components (top soil, compost, coconut coir) thoroughly</li>
                  <li>5. Apply the soil mix as your main growing layer</li>
                  <li>6. Top with mulch layer (dry leaves, shredded fronds)</li>
                  <li>7. Water gently to settle all layers</li>
                </ol>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
}
