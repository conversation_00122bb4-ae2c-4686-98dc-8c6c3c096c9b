import { useState } from "react";
import { Link } from "@remix-run/react";
import { MetaFunction } from "@remix-run/node";
import { ErrorBoundary } from "../components/ErrorBoundary";

export const meta: MetaFunction = () => {
  return [
    { title: "Soil Mix Calculator - GardenPal" },
    { name: "description", content: "Create custom soil mixes for your plants" },
  ];
};

export { ErrorBoundary };

interface SoilComponent {
  name: string;
  description: string;
  benefits: string[];
  percentage: number;
}

interface SoilMix {
  name: string;
  description: string;
  components: SoilComponent[];
  bestFor: string[];
}

const soilComponents: Record<string, Omit<SoilComponent, 'percentage'>> = {
  pottingSoil: {
    name: "Potting Soil",
    description: "Base organic matter with nutrients",
    benefits: ["Provides nutrients", "Retains moisture", "Good structure"]
  },
  perlite: {
    name: "Perlite",
    description: "Volcanic glass for drainage",
    benefits: ["Improves drainage", "Prevents compaction", "Lightweight"]
  },
  vermiculite: {
    name: "Vermiculite",
    description: "Mineral that retains water and nutrients",
    benefits: ["Water retention", "Nutrient retention", "Aeration"]
  },
  cocoFiber: {
    name: "Coco Fiber",
    description: "Coconut husk fiber",
    benefits: ["Sustainable", "Good drainage", "pH neutral"]
  },
  barkChips: {
    name: "Bark Chips",
    description: "Chunky organic matter",
    benefits: ["Excellent drainage", "Aeration", "Slow decomposition"]
  },
  sand: {
    name: "Coarse Sand",
    description: "Improves drainage",
    benefits: ["Fast drainage", "Prevents waterlogging", "Weight"]
  }
};

const presetMixes: SoilMix[] = [
  {
    name: "Tropical Plants Mix",
    description: "Perfect for Monstera, Pothos, and other tropical houseplants",
    components: [
      { ...soilComponents.pottingSoil, percentage: 50 },
      { ...soilComponents.perlite, percentage: 30 },
      { ...soilComponents.barkChips, percentage: 20 }
    ],
    bestFor: ["Monstera", "Pothos", "Philodendron", "Rubber Plant"]
  },
  {
    name: "Succulent & Cactus Mix",
    description: "Fast-draining mix for desert plants",
    components: [
      { ...soilComponents.pottingSoil, percentage: 40 },
      { ...soilComponents.perlite, percentage: 30 },
      { ...soilComponents.sand, percentage: 30 }
    ],
    bestFor: ["Succulents", "Cacti", "Snake Plant", "Aloe"]
  },
  {
    name: "Moisture-Loving Mix",
    description: "Retains moisture for humidity-loving plants",
    components: [
      { ...soilComponents.pottingSoil, percentage: 60 },
      { ...soilComponents.vermiculite, percentage: 25 },
      { ...soilComponents.cocoFiber, percentage: 15 }
    ],
    bestFor: ["Peace Lily", "Ferns", "Calathea", "Boston Ivy"]
  }
];

export default function SoilCalculator() {
  const [selectedPreset, setSelectedPreset] = useState<SoilMix | null>(null);
  const [customMix, setCustomMix] = useState<SoilComponent[]>([
    { ...soilComponents.pottingSoil, percentage: 50 },
    { ...soilComponents.perlite, percentage: 30 },
    { ...soilComponents.barkChips, percentage: 20 }
  ]);
  const [potSize, setPotSize] = useState<number>(6);
  const [activeTab, setActiveTab] = useState<"presets" | "custom">("presets");

  const calculateVolume = (diameter: number): number => {
    // Simplified calculation: assume pot height ≈ diameter
    const radius = diameter / 2;
    const height = diameter * 0.8; // Typical pot proportions
    return Math.PI * radius * radius * height;
  };

  const calculateAmounts = (mix: SoilComponent[], volume: number) => {
    return mix.map(component => ({
      ...component,
      amount: Math.round((component.percentage / 100) * volume * 10) / 10
    }));
  };

  const updateCustomPercentage = (index: number, percentage: number) => {
    const newMix = [...customMix];
    newMix[index].percentage = percentage;
    setCustomMix(newMix);
  };

  const addCustomComponent = (componentKey: string) => {
    const component = soilComponents[componentKey];
    if (component && !customMix.find(c => c.name === component.name)) {
      setCustomMix([...customMix, { ...component, percentage: 10 }]);
    }
  };

  const removeCustomComponent = (index: number) => {
    if (customMix.length > 1) {
      const newMix = customMix.filter((_, i) => i !== index);
      setCustomMix(newMix);
    }
  };

  const totalPercentage = customMix.reduce((sum, component) => sum + component.percentage, 0);
  const volume = calculateVolume(potSize);
  const currentMix = selectedPreset ? selectedPreset.components : customMix;
  const amounts = calculateAmounts(currentMix, volume);

  return (
    <div className="min-h-screen flex flex-col">
      <header className="bg-emerald-600 text-white p-4 shadow-md">
        <div className="container mx-auto flex justify-between items-center">
          <Link to="/" className="text-2xl font-bold">GardenPal</Link>
          <Link
            to="/library"
            className="bg-white text-emerald-600 hover:bg-gray-100 font-bold py-1 px-3 rounded-lg text-sm transition-colors"
          >
            Plant Library
          </Link>
        </div>
      </header>

      <main className="flex-grow container mx-auto p-4 md:p-6">
        <div className="max-w-4xl mx-auto">
          <div className="mb-8">
            <h1 className="text-3xl font-bold mb-4">Soil Mix Calculator</h1>
            <p className="text-gray-600 dark:text-gray-300">
              Create the perfect soil mix for your plants. Choose from preset mixes or create your own custom blend.
            </p>
          </div>

          {/* Pot Size Input */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">Pot Size</h2>
            <div className="flex items-center space-x-4">
              <label className="text-sm font-medium">Diameter (inches):</label>
              <input
                type="number"
                min="2"
                max="24"
                value={potSize}
                onChange={(e) => setPotSize(Number(e.target.value))}
                className="w-20 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
              <span className="text-sm text-gray-500">
                ≈ {Math.round(volume)} cubic inches of soil needed
              </span>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden mb-6">
            <div className="flex border-b border-gray-200 dark:border-gray-700">
              <button
                className={`flex-1 py-3 px-4 text-center font-medium ${
                  activeTab === "presets"
                    ? "text-emerald-600 border-b-2 border-emerald-600 bg-emerald-50 dark:bg-emerald-900/20"
                    : "text-gray-500 hover:text-emerald-600"
                }`}
                onClick={() => {
                  setActiveTab("presets");
                  setSelectedPreset(null);
                }}
              >
                Preset Mixes
              </button>
              <button
                className={`flex-1 py-3 px-4 text-center font-medium ${
                  activeTab === "custom"
                    ? "text-emerald-600 border-b-2 border-emerald-600 bg-emerald-50 dark:bg-emerald-900/20"
                    : "text-gray-500 hover:text-emerald-600"
                }`}
                onClick={() => {
                  setActiveTab("custom");
                  setSelectedPreset(null);
                }}
              >
                Custom Mix
              </button>
            </div>

            <div className="p-6">
              {activeTab === "presets" && (
                <div className="space-y-4">
                  {presetMixes.map((mix, index) => (
                    <div
                      key={index}
                      className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                        selectedPreset === mix
                          ? "border-emerald-500 bg-emerald-50 dark:bg-emerald-900/20"
                          : "border-gray-200 dark:border-gray-600 hover:border-emerald-300"
                      }`}
                      onClick={() => setSelectedPreset(mix)}
                    >
                      <h3 className="font-semibold text-lg mb-2">{mix.name}</h3>
                      <p className="text-gray-600 dark:text-gray-300 text-sm mb-3">{mix.description}</p>
                      <div className="flex flex-wrap gap-2 mb-3">
                        {mix.bestFor.map((plant, plantIndex) => (
                          <span
                            key={plantIndex}
                            className="bg-emerald-100 text-emerald-800 text-xs px-2 py-1 rounded-full"
                          >
                            {plant}
                          </span>
                        ))}
                      </div>
                      <div className="text-sm">
                        <strong>Components:</strong> {mix.components.map(c => `${c.name} (${c.percentage}%)`).join(", ")}
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {activeTab === "custom" && (
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <h3 className="font-semibold">Custom Mix Components</h3>
                    <span className={`text-sm ${totalPercentage === 100 ? 'text-green-600' : 'text-red-600'}`}>
                      Total: {totalPercentage}%
                    </span>
                  </div>

                  {customMix.map((component, index) => (
                    <div key={index} className="flex items-center space-x-4 p-3 border border-gray-200 dark:border-gray-600 rounded-lg">
                      <div className="flex-1">
                        <div className="font-medium">{component.name}</div>
                        <div className="text-sm text-gray-500">{component.description}</div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input
                          type="number"
                          min="0"
                          max="100"
                          value={component.percentage}
                          onChange={(e) => updateCustomPercentage(index, Number(e.target.value))}
                          className="w-16 px-2 py-1 border border-gray-300 rounded focus:ring-2 focus:ring-emerald-500 focus:border-emerald-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                        />
                        <span className="text-sm">%</span>
                        {customMix.length > 1 && (
                          <button
                            onClick={() => removeCustomComponent(index)}
                            className="text-red-500 hover:text-red-700 p-1"
                          >
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                            </svg>
                          </button>
                        )}
                      </div>
                    </div>
                  ))}

                  <div className="border-t pt-4">
                    <h4 className="font-medium mb-2">Add Component:</h4>
                    <div className="flex flex-wrap gap-2">
                      {Object.entries(soilComponents).map(([key, component]) => (
                        <button
                          key={key}
                          onClick={() => addCustomComponent(key)}
                          disabled={customMix.find(c => c.name === component.name) !== undefined}
                          className="px-3 py-1 text-sm border border-gray-300 rounded-full hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed dark:border-gray-600 dark:hover:bg-gray-700"
                        >
                          {component.name}
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Results */}
          {(selectedPreset || activeTab === "custom") && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
              <h2 className="text-xl font-semibold mb-4">
                {selectedPreset ? selectedPreset.name : "Custom Mix"} - Shopping List
              </h2>

              {activeTab === "custom" && totalPercentage !== 100 && (
                <div className="bg-yellow-50 border border-yellow-200 text-yellow-800 px-4 py-3 rounded-lg mb-4">
                  <strong>Warning:</strong> Your mix percentages don't add up to 100%.
                  Adjust the percentages for accurate measurements.
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {amounts.map((component, index) => (
                  <div key={index} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="font-medium">{component.name}</h3>
                      <span className="text-lg font-bold text-emerald-600">
                        {component.amount} cu in
                      </span>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-300 mb-2">
                      {component.description}
                    </p>
                    <div className="text-xs text-gray-500">
                      {component.percentage}% of total mix
                    </div>
                  </div>
                ))}
              </div>

              <div className="mt-6 p-4 bg-emerald-50 dark:bg-emerald-900/20 rounded-lg">
                <h3 className="font-medium text-emerald-800 dark:text-emerald-300 mb-2">Mixing Instructions:</h3>
                <ol className="text-sm text-emerald-700 dark:text-emerald-300 space-y-1">
                  <li>1. Gather all components in the amounts listed above</li>
                  <li>2. Mix dry components (perlite, bark chips, sand) first</li>
                  <li>3. Add potting soil and mix thoroughly</li>
                  <li>4. Add any moisture-retaining components (vermiculite, coco fiber) last</li>
                  <li>5. Mix until evenly distributed</li>
                  <li>6. Moisten slightly before potting your plant</li>
                </ol>
              </div>
            </div>
          )}
        </div>
      </main>
    </div>
  );
}
