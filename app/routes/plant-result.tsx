import { useState, useEffect } from "react";
import { Link, useSearchParams } from "@remix-run/react";
import { MetaFunction } from "@remix-run/node";


export const meta: MetaFunction = () => {
  return [
    { title: "Plant Identification Result - PlantPal" },
    { name: "description", content: "View your plant identification results with detailed care information." },
  ];
};

interface PlantIdentificationResult {
  id: string;
  name: string;
  scientificName: string;
  commonNames: string[];
  confidence: number;
  description?: string;
  image?: string;
  similarImages: Array<{
    url: string;
    similarity: number;
  }>;
  taxonomy?: {
    family?: string;
    genus?: string;
    class?: string;
  };
  isPlantProbability: number;
  topMatches: Array<{
    name: string;
    scientificName: string;
    confidence: number;
    image?: string;
  }>;
}

export default function PlantResult() {
  const [searchParams] = useSearchParams();
  const [result, setResult] = useState<PlantIdentificationResult | null>(null);
  const [lowConfidence, setLowConfidence] = useState(false);
  const [selectedMatch, setSelectedMatch] = useState<number>(0);

  useEffect(() => {
    // Get result from sessionStorage
    const storedResult = sessionStorage.getItem('plantIdentificationResult');
    const storedLowConfidence = sessionStorage.getItem('plantIdentificationLowConfidence');

    if (storedResult) {
      try {
        const parsedResult = JSON.parse(storedResult);
        setResult(parsedResult);
        setLowConfidence(storedLowConfidence === 'true');

        // Clear stored data
        sessionStorage.removeItem('plantIdentificationResult');
        sessionStorage.removeItem('plantIdentificationLowConfidence');
      } catch (error) {
        console.error('Error parsing stored result:', error);
      }
    }
  }, []);

  if (!result) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <header className="bg-emerald-600 text-white p-4 shadow-md">
        <div className="container mx-auto flex justify-between items-center">
          <Link to="/" className="text-2xl font-bold">PlantPal</Link>
        </div>
      </header>
        <main className="flex-grow container mx-auto p-3 sm:p-4 md:p-6">
          <div className="max-w-4xl mx-auto">
            <div className="text-center py-12">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                No identification result found
              </h1>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                Please go back and identify a plant first.
              </p>
              <Link
                to="/identify"
                className="inline-flex items-center px-6 py-3 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors"
              >
                Identify a Plant
              </Link>
            </div>
          </div>
        </main>
      </div>
    );
  }

  const currentMatch = result.topMatches[selectedMatch] || result.topMatches[0];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <header className="bg-emerald-600 text-white p-4 shadow-md">
        <div className="container mx-auto flex justify-between items-center">
          <Link to="/" className="text-2xl font-bold">PlantPal</Link>
        </div>
      </header>
      <main className="flex-grow container mx-auto p-3 sm:p-4 md:p-6">
        <div className="max-w-4xl mx-auto">
          {/* Back Button */}
          <div className="mb-4 sm:mb-6">
            <Link
              to="/identify"
              className="inline-flex items-center text-emerald-600 hover:text-emerald-700 dark:text-emerald-400 dark:hover:text-emerald-300"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              Back to Identify
            </Link>
          </div>

          {/* Low Confidence Warning */}
          {lowConfidence && (
            <div className="mb-4 sm:mb-6 p-4 bg-yellow-50 border border-yellow-200 text-yellow-800 rounded-lg">
              <div className="flex items-start">
                <svg className="w-5 h-5 text-yellow-600 mr-3 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
                <div>
                  <h3 className="font-medium">Low Confidence Identification</h3>
                  <p className="text-sm mt-1">
                    The identification confidence is below 70%. Please review the alternative matches below or try taking another photo with better lighting and focus.
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Main Result Card */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden mb-6">
            <div className="md:flex">
              {/* Image */}
              <div className="md:w-1/2">
                {result.image ? (
                  <img
                    src={result.image}
                    alt={result.name}
                    className="w-full h-64 md:h-full object-cover"
                  />
                ) : (
                  <div className="w-full h-64 md:h-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                    <svg className="w-16 h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  </div>
                )}
              </div>

              {/* Content */}
              <div className="md:w-1/2 p-4 sm:p-6">
                <div className="mb-4">
                  <h1 className="text-2xl sm:text-3xl font-bold mb-2 text-gray-900 dark:text-white">
                    {result.name}
                  </h1>
                  <p className="text-gray-600 dark:text-gray-300 text-base sm:text-lg mb-3">
                    <em>{result.scientificName}</em>
                  </p>

                  {/* Confidence Score */}
                  <div className="mb-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Confidence Score
                      </span>
                      <span className={`text-sm font-bold ${
                        result.confidence >= 0.8 ? 'text-green-600' :
                        result.confidence >= 0.6 ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        {Math.round(result.confidence * 100)}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${
                          result.confidence >= 0.8 ? 'bg-green-500' :
                          result.confidence >= 0.6 ? 'bg-yellow-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${result.confidence * 100}%` }}
                      ></div>
                    </div>
                  </div>

                  {/* Taxonomy */}
                  {result.taxonomy && (
                    <div className="mb-4">
                      <h3 className="font-medium text-gray-800 dark:text-gray-200 mb-2">Classification</h3>
                      <div className="space-y-1 text-sm text-gray-600 dark:text-gray-300">
                        {result.taxonomy.family && <p><strong>Family:</strong> {result.taxonomy.family}</p>}
                        {result.taxonomy.genus && <p><strong>Genus:</strong> {result.taxonomy.genus}</p>}
                        {result.taxonomy.class && <p><strong>Class:</strong> {result.taxonomy.class}</p>}
                      </div>
                    </div>
                  )}

                  {/* Common Names */}
                  {result.commonNames.length > 1 && (
                    <div className="mb-4">
                      <h3 className="font-medium text-gray-800 dark:text-gray-200 mb-2">Common Names</h3>
                      <div className="flex flex-wrap gap-2">
                        {result.commonNames.slice(0, 5).map((name, index) => (
                          <span
                            key={index}
                            className="bg-emerald-100 text-emerald-800 text-xs px-2 py-1 rounded-full"
                          >
                            {name}
                          </span>
                        ))}
                      </div>
                    </div>
                  )}
                </div>

                {/* Description */}
                {result.description && (
                  <div className="mb-4">
                    <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed">
                      {result.description}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Alternative Matches */}
          {result.topMatches.length > 1 && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 sm:p-6 mb-6">
              <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
                Alternative Matches
              </h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                {result.topMatches.map((match, index) => (
                  <div
                    key={index}
                    className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                      selectedMatch === index
                        ? "border-emerald-500 bg-emerald-50 dark:bg-emerald-900/20"
                        : "border-gray-200 dark:border-gray-600 hover:border-emerald-300"
                    }`}
                    onClick={() => setSelectedMatch(index)}
                  >
                    {match.image && (
                      <img
                        src={match.image}
                        alt={match.name}
                        className="w-full h-32 object-cover rounded-lg mb-3"
                      />
                    )}
                    <h3 className="font-semibold text-sm mb-1 text-gray-900 dark:text-white">
                      {match.name}
                    </h3>
                    <p className="text-xs text-gray-600 dark:text-gray-300 mb-2">
                      <em>{match.scientificName}</em>
                    </p>
                    <div className="flex items-center justify-between">
                      <span className="text-xs text-gray-500">Confidence</span>
                      <span className={`text-xs font-bold ${
                        match.confidence >= 0.8 ? 'text-green-600' :
                        match.confidence >= 0.6 ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        {Math.round(match.confidence * 100)}%
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Similar Images */}
          {result.similarImages.length > 0 && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 sm:p-6 mb-6">
              <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
                Similar Images
              </h2>
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4">
                {result.similarImages.slice(0, 10).map((image, index) => (
                  <div key={index} className="relative group">
                    <img
                      src={image.url}
                      alt={`Similar plant ${index + 1}`}
                      className="w-full h-24 object-cover rounded-lg"
                    />
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-opacity rounded-lg flex items-center justify-center">
                      <span className="text-white text-xs opacity-0 group-hover:opacity-100 transition-opacity">
                        {Math.round(image.similarity * 100)}% match
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <Link
                to="/identify"
                className="flex-1 bg-emerald-600 text-white px-6 py-3 rounded-lg hover:bg-emerald-700 transition-colors text-center font-medium"
              >
                Identify Another Plant
              </Link>
              <Link
                to="/library"
                className="flex-1 bg-gray-200 text-gray-800 px-6 py-3 rounded-lg hover:bg-gray-300 transition-colors text-center font-medium dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
              >
                Browse Plant Library
              </Link>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}