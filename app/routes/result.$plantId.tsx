import { useState, useEffect } from "react";
import { Link, useParams } from "@remix-run/react";
import { MetaFunction } from "@remix-run/node";
import { getManagedPlantById, SavedPlantData } from "../utils/plantManager";
import { ErrorBoundary } from "../components/ErrorBoundary";

export { ErrorBoundary };

export const meta: MetaFunction = () => {
  return [
    { title: "Plant Details - PlantPal" },
    { name: "description", content: "View detailed plant care information from your library." },
  ];
};

// Create a mapping from slugs to plant IDs for backward compatibility
const slugToPlantId: Record<string, string> = {
  "monstera-deliciosa": "monstera-deliciosa",
  "pothos": "pothos",
  "devils-ivy": "pothos",
  "snake-plant": "snake-plant",
  "mother-in-laws-tongue": "snake-plant",
  "fiddle-leaf-fig": "fiddle-leaf-fig",
  "rubber-plant": "rubber-plant",
  "rubber-tree": "rubber-plant",
  "peace-lily": "peace-lily"
};

export default function PlantDetail() {
  const { plantId } = useParams();
  const [plant, setPlant] = useState<SavedPlantData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (plantId) {
      // Try direct ID lookup first (for saved plants)
      let foundPlant = getManagedPlantById(plantId);

      // If not found, try slug mapping (for default plants)
      if (!foundPlant) {
        const mappedId = slugToPlantId[plantId];
        if (mappedId) {
          foundPlant = getManagedPlantById(mappedId);
        }
      }

      setPlant(foundPlant || null);
      setLoading(false);
    }
  }, [plantId]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <header className="bg-emerald-600 text-white p-4 shadow-md">
          <div className="container mx-auto flex justify-between items-center">
            <Link to="/" className="text-2xl font-bold">PlantPal</Link>
          </div>
        </header>
        <main className="flex-grow container mx-auto p-3 sm:p-4 md:p-6">
          <div className="max-w-4xl mx-auto">
            <div className="text-center py-12">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-emerald-600 mx-auto mb-4"></div>
              <p className="text-gray-600 dark:text-gray-300">Loading plant details...</p>
            </div>
          </div>
        </main>
      </div>
    );
  }

  if (!plant) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        <header className="bg-emerald-600 text-white p-4 shadow-md">
          <div className="container mx-auto flex justify-between items-center">
            <Link to="/" className="text-2xl font-bold">PlantPal</Link>
          </div>
        </header>
        <main className="flex-grow container mx-auto p-3 sm:p-4 md:p-6">
          <div className="max-w-4xl mx-auto">
            <div className="text-center py-12">
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                Plant not found
              </h1>
              <p className="text-gray-600 dark:text-gray-300 mb-6">
                The plant you're looking for doesn't exist or has been removed.
              </p>
              <Link
                to="/library"
                className="inline-flex items-center px-6 py-3 bg-emerald-600 text-white rounded-lg hover:bg-emerald-700 transition-colors"
              >
                Back to Library
              </Link>
            </div>
          </div>
        </main>
      </div>
    );
  }

  // Check if this plant has full identification result data
  const hasFullData = plant.identificationResult;
  const result = plant.identificationResult;

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <header className="bg-emerald-600 text-white p-4 shadow-md">
        <div className="container mx-auto flex justify-between items-center">
          <Link to="/" className="text-2xl font-bold">PlantPal</Link>
        </div>
      </header>
      <main className="flex-grow container mx-auto p-3 sm:p-4 md:p-6">
        <div className="max-w-4xl mx-auto">
          {/* Back Button */}
          <div className="mb-4 sm:mb-6">
            <Link
              to="/library"
              className="inline-flex items-center text-emerald-600 hover:text-emerald-700 dark:text-emerald-400 dark:hover:text-emerald-300"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
              Back to Library
            </Link>
          </div>

          {/* Show full identification result if available, otherwise show basic plant info */}
          {hasFullData && result ? (
            <>
              {/* Main Result Card - Full Identification Data */}
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden mb-6">
                <div className="md:flex">
                  {/* Image */}
                  <div className="md:w-1/2">
                    {result.image ? (
                      <img
                        src={result.image}
                        alt={result.name}
                        className="w-full h-64 md:h-full object-cover"
                      />
                    ) : (
                      <div className="w-full h-64 md:h-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
                        <svg className="w-16 h-16 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                      </div>
                    )}
                  </div>

                  {/* Content */}
                  <div className="md:w-1/2 p-4 sm:p-6">
                    <div className="mb-4">
                      <h1 className="text-2xl sm:text-3xl font-bold mb-2 text-gray-900 dark:text-white">
                        {result.name}
                      </h1>
                      <p className="text-gray-600 dark:text-gray-300 text-base sm:text-lg mb-3">
                        <em>{result.scientificName}</em>
                      </p>

                      {/* Confidence Score */}
                      <div className="mb-4">
                        <div className="flex items-center justify-between mb-2">
                          <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            AI Confidence Score
                          </span>
                          <span className={`text-sm font-bold ${
                            result.confidence >= 0.8 ? 'text-green-600' :
                            result.confidence >= 0.6 ? 'text-yellow-600' : 'text-red-600'
                          }`}>
                            {Math.round(result.confidence * 100)}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full ${
                              result.confidence >= 0.8 ? 'bg-green-500' :
                              result.confidence >= 0.6 ? 'bg-yellow-500' : 'bg-red-500'
                            }`}
                            style={{ width: `${result.confidence * 100}%` }}
                          ></div>
                        </div>
                      </div>

                      {/* Taxonomy */}
                      {result.taxonomy && (
                        <div className="mb-4">
                          <h3 className="font-medium text-gray-800 dark:text-gray-200 mb-2">Classification</h3>
                          <div className="space-y-1 text-sm text-gray-600 dark:text-gray-300">
                            {result.taxonomy.family && <p><strong>Family:</strong> {result.taxonomy.family}</p>}
                            {result.taxonomy.genus && <p><strong>Genus:</strong> {result.taxonomy.genus}</p>}
                            {result.taxonomy.class && <p><strong>Class:</strong> {result.taxonomy.class}</p>}
                          </div>
                        </div>
                      )}

                      {/* Common Names */}
                      {result.commonNames.length > 1 && (
                        <div className="mb-4">
                          <h3 className="font-medium text-gray-800 dark:text-gray-200 mb-2">Common Names</h3>
                          <div className="flex flex-wrap gap-2">
                            {result.commonNames.slice(0, 5).map((name, index) => (
                              <span
                                key={index}
                                className="bg-emerald-100 text-emerald-800 text-xs px-2 py-1 rounded-full"
                              >
                                {name}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Description */}
                    {result.description && (
                      <div className="mb-4">
                        <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed">
                          {result.description}
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </>
          ) : (
            /* Basic Plant Card - Default Plants */
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden mb-6">
              <div className="md:flex">
                <div className="md:w-1/2">
                  <img
                    src={plant.image}
                    alt={plant.name}
                    className="w-full h-64 md:h-full object-cover"
                  />
                </div>
                <div className="md:w-1/2 p-4 sm:p-6">
                  <h1 className="text-2xl sm:text-3xl font-bold mb-2 text-gray-900 dark:text-white">
                    {plant.name}
                  </h1>
                  <p className="text-gray-600 dark:text-gray-300 text-base sm:text-lg mb-3">
                    <em>{plant.scientificName}</em>
                  </p>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                    <strong>Common Name:</strong> {plant.commonName}
                  </p>
                  <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed">
                    {plant.description}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Comprehensive Care Guide */}
          {hasFullData && result ? (
            /* Full Care Guide with Custom Soil Mix for Identified Plants */
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 sm:p-6 mb-6">
              <h2 className="text-xl font-semibold mb-6 text-gray-900 dark:text-white flex items-center">
                <svg className="h-6 w-6 text-emerald-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
                Complete Care Guide
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Basic Care Requirements */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Essential Care</h3>

                  {/* Light */}
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-yellow-500 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">Light Requirements</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">{result.careGuide.light}</p>
                    </div>
                  </div>

                  {/* Water */}
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-blue-500 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M5.05 3.636a1 1 0 010 1.414 7 7 0 000 9.9 1 1 0 11-1.414 1.414 9 9 0 010-12.728 1 1 0 011.414 0zm9.9 0a1 1 0 011.414 0 9 9 0 010 12.728 1 1 0 11-1.414-1.414 7 7 0 000-9.9 1 1 0 010-1.414zM7.879 6.464a1 1 0 010 1.414 3 3 0 000 4.243 1 1 0 11-1.415 1.414 5 5 0 010-7.07 1 1 0 011.415 0zm4.242 0a1 1 0 011.415 0 5 5 0 010 7.072 1 1 0 01-1.415-1.415 3 3 0 000-4.242 1 1 0 010-1.415zM10 9a1 1 0 011 1v.01a1 1 0 11-2 0V10a1 1 0 011-1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">Watering</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">{result.careGuide.water}</p>
                    </div>
                  </div>

                  {/* Humidity */}
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-teal-500 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">Humidity</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">{result.careGuide.humidity}</p>
                    </div>
                  </div>

                  {/* Temperature */}
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-red-500 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">Temperature</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">{result.careGuide.temperature}</p>
                    </div>
                  </div>
                </div>

                {/* Advanced Care */}
                <div className="space-y-4">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Advanced Care</h3>

                  {/* Soil */}
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-amber-600 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                        <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">Soil Requirements</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">{result.careGuide.soil}</p>
                    </div>
                  </div>

                  {/* Fertilizer */}
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-green-500 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">Fertilizing</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">{result.careGuide.fertilizer}</p>
                    </div>
                  </div>

                  {/* Repotting */}
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0">
                      <svg className="h-5 w-5 text-purple-500 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">Repotting</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">{result.careGuide.repotting}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Common Issues & Solutions */}
              <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Common Issues & Solutions</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {result.careGuide.commonIssues.map((issue, index) => (
                    <div key={index} className="flex items-start space-x-2">
                      <svg className="h-4 w-4 text-red-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                      </svg>
                      <span className="text-sm text-gray-600 dark:text-gray-300">{issue}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Pro Tips */}
              <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Pro Tips</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {result.careGuide.tips.map((tip, index) => (
                    <div key={index} className="flex items-start space-x-2">
                      <svg className="h-4 w-4 text-emerald-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      <span className="text-sm text-gray-600 dark:text-gray-300">{tip}</span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Custom Soil Mix Calculator */}
              <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4 flex items-center">
                  <svg className="h-5 w-5 text-amber-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                  </svg>
                  Custom Soil Mix Recipe
                </h3>

                <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4 mb-6">
                  <p className="text-amber-800 dark:text-amber-200 text-sm font-medium mb-2">
                    {result.careGuide.customSoilMix.description}
                  </p>
                  <p className="text-amber-700 dark:text-amber-300 text-sm">
                    <strong>Total Volume:</strong> {result.careGuide.customSoilMix.totalVolume}
                  </p>
                </div>

                {/* Bucket Measurements */}
                <div className="mb-6">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-3">Bucket Measurements (20L each)</h4>
                  <div className="space-y-3">
                    {result.careGuide.customSoilMix.bucketMeasurements.map((measurement, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3">
                            <div className="flex-shrink-0">
                              <div className="w-8 h-8 bg-emerald-100 dark:bg-emerald-900 rounded-full flex items-center justify-center">
                                <span className="text-emerald-600 dark:text-emerald-400 font-bold text-sm">
                                  {measurement.buckets}
                                </span>
                              </div>
                            </div>
                            <div>
                              <p className="font-medium text-gray-900 dark:text-white">{measurement.material}</p>
                              <p className="text-sm text-gray-600 dark:text-gray-300">{measurement.purpose}</p>
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-bold text-emerald-600 dark:text-emerald-400">
                            {measurement.buckets} bucket{measurement.buckets !== 1 ? 's' : ''}
                          </p>
                          <p className="text-sm text-gray-500 dark:text-gray-400">{measurement.liters}L</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Layering Guide */}
                <div className="mb-6">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-3">Garden Bed Layering</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                      <h5 className="font-medium text-blue-800 dark:text-blue-200 mb-2">Bottom Layer</h5>
                      <ul className="space-y-1">
                        {result.careGuide.customSoilMix.layering.bottom.map((item, index) => (
                          <li key={index} className="text-sm text-blue-700 dark:text-blue-300">• {item}</li>
                        ))}
                      </ul>
                    </div>
                    <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                      <h5 className="font-medium text-green-800 dark:text-green-200 mb-2">Middle Layer</h5>
                      <ul className="space-y-1">
                        {result.careGuide.customSoilMix.layering.middle.map((item, index) => (
                          <li key={index} className="text-sm text-green-700 dark:text-green-300">• {item}</li>
                        ))}
                      </ul>
                    </div>
                    <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                      <h5 className="font-medium text-yellow-800 dark:text-yellow-200 mb-2">Top Layer</h5>
                      <ul className="space-y-1">
                        {result.careGuide.customSoilMix.layering.top.map((item, index) => (
                          <li key={index} className="text-sm text-yellow-700 dark:text-yellow-300">• {item}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Preparation Steps */}
                <div className="mb-6">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-3">Preparation Steps</h4>
                  <div className="space-y-2">
                    {result.careGuide.customSoilMix.preparation.map((step, index) => (
                      <div key={index} className="flex items-start space-x-3">
                        <div className="flex-shrink-0">
                          <div className="w-6 h-6 bg-emerald-500 text-white rounded-full flex items-center justify-center text-xs font-bold">
                            {index + 1}
                          </div>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-300">{step}</p>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Important Notes */}
                <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 dark:text-white mb-3">Important Notes</h4>
                  <div className="space-y-2">
                    {result.careGuide.customSoilMix.notes.map((note, index) => (
                      <div key={index} className="flex items-start space-x-2">
                        <svg className="h-4 w-4 text-blue-500 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                        </svg>
                        <span className="text-sm text-gray-600 dark:text-gray-300">{note}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ) : (
            /* Basic Care Guide for Default Plants */
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 sm:p-6 mb-6">
              <h2 className="text-xl font-semibold mb-6 text-gray-900 dark:text-white flex items-center">
                <svg className="h-6 w-6 text-emerald-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
                Care Guide
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {/* Light */}
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <svg className="h-6 w-6 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-white mb-1">Light</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300">{plant.care.light}</p>
                  </div>
                </div>

                {/* Water */}
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <svg className="h-6 w-6 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M5.05 3.636a1 1 0 010 1.414 7 7 0 000 9.9 1 1 0 11-1.414 1.414 9 9 0 010-12.728 1 1 0 011.414 0zm9.9 0a1 1 0 011.414 0 9 9 0 010 12.728 1 1 0 11-1.414-1.414 7 7 0 000-9.9 1 1 0 010-1.414zM7.879 6.464a1 1 0 010 1.414 3 3 0 000 4.243 1 1 0 11-1.415 1.414 5 5 0 010-7.07 1 1 0 011.415 0zm4.242 0a1 1 0 011.415 0 5 5 0 010 7.072 1 1 0 01-1.415-1.415 3 3 0 000-4.242 1 1 0 010-1.415zM10 9a1 1 0 011 1v.01a1 1 0 11-2 0V10a1 1 0 011-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-white mb-1">Water</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300">{plant.care.water}</p>
                  </div>
                </div>

                {/* Other care items... */}
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0">
                    <svg className="h-6 w-6 text-teal-500" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-white mb-1">Humidity</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300">{plant.care.humidity}</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Actions */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-4 sm:p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <Link
                to="/identify"
                className="flex-1 bg-emerald-600 text-white px-6 py-3 rounded-lg hover:bg-emerald-700 transition-colors text-center font-medium"
              >
                Identify Another Plant
              </Link>
              <Link
                to="/library"
                className="flex-1 bg-gray-200 text-gray-800 px-6 py-3 rounded-lg hover:bg-gray-300 transition-colors text-center font-medium dark:bg-gray-700 dark:text-gray-200 dark:hover:bg-gray-600"
              >
                Back to Library
              </Link>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
