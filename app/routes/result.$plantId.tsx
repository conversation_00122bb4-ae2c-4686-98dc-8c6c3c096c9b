import { useState, useEffect } from "react";
import { Link, useParams } from "@remix-run/react";
import { MetaFunction } from "@remix-run/node";

export const meta: MetaFunction = () => {
  return [
    { title: "Plant Details - GardenPal" },
    { name: "description", content: "Detailed information about your identified plant" },
  ];
};

import { Plant } from "../data/plants";
import { getManagedPlantById } from "../utils/plantManager";
import { ErrorBoundary } from "../components/ErrorBoundary";

export { ErrorBoundary };

// Create a mapping from slugs to plant IDs
const slugToPlantId: Record<string, string> = {
  "monstera-deliciosa": "monstera-deliciosa",
  "pothos": "pothos",
  "devils-ivy": "pothos",
  "snake-plant": "snake-plant",
  "mother-in-laws-tongue": "snake-plant",
  "fiddle-leaf-fig": "fiddle-leaf-fig",
  "rubber-plant": "rubber-plant",
  "rubber-tree": "rubber-plant",
  "peace-lily": "peace-lily"
};

export default function PlantResult() {
  const { plantId } = useParams();
  const [activeTab, setActiveTab] = useState("care");
  const [identificationResult, setIdentificationResult] = useState<Plant | null>(null);

  // Check for identification result from sessionStorage first
  useEffect(() => {
    const storedResult = sessionStorage.getItem('plantIdentificationResult');
    if (storedResult) {
      try {
        const result = JSON.parse(storedResult);
        setIdentificationResult(result);
        // Clear the stored result after using it
        sessionStorage.removeItem('plantIdentificationResult');
      } catch (error) {
        console.error('Error parsing identification result:', error);
      }
    }
  }, []);

  // Get plant data - prioritize identification result, then fall back to managed plants
  // First try direct ID lookup, then try slug mapping for backward compatibility
  let databasePlant = null;
  if (plantId) {
    // Try direct ID lookup first (for saved plants)
    databasePlant = getManagedPlantById(plantId);

    // If not found, try slug mapping (for default plants)
    if (!databasePlant) {
      const mappedId = slugToPlantId[plantId];
      if (mappedId) {
        databasePlant = getManagedPlantById(mappedId);
      }
    }
  }

  const plant = identificationResult || databasePlant;

  if (!plant) {
    return (
      <div className="min-h-screen flex flex-col">
        <header className="bg-emerald-600 text-white p-4 shadow-md">
          <div className="container mx-auto flex justify-between items-center">
            <Link to="/" className="text-2xl font-bold">GardenPal</Link>
          </div>
        </header>

        <main className="flex-grow container mx-auto p-4 md:p-6 flex items-center justify-center">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 max-w-md w-full text-center">
            <h1 className="text-xl font-bold mb-4">Plant Not Found</h1>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              Sorry, we couldn't find information about this plant.
            </p>
            <Link
              to="/identify"
              className="inline-block bg-emerald-600 hover:bg-emerald-700 text-white font-bold py-2 px-4 rounded-lg transition-colors"
            >
              Try Another Plant
            </Link>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <header className="bg-emerald-600 text-white p-4 shadow-md">
        <div className="container mx-auto flex justify-between items-center">
          <Link to="/" className="text-2xl font-bold">GardenPal</Link>
          <Link
            to="/identify"
            className="bg-white text-emerald-600 hover:bg-gray-100 font-bold py-1 px-3 rounded-lg text-sm transition-colors"
          >
            New Identification
          </Link>
        </div>
      </header>

      <main className="flex-grow container mx-auto p-4 md:p-6">
        <div className="max-w-3xl mx-auto bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
          <div className="md:flex">
            <div className="md:w-1/2 h-64 md:h-auto">
              <img
                src={plant.image}
                alt={plant.name}
                className="w-full h-full object-cover"
              />
            </div>
            <div className="p-6 md:w-1/2">
              <h1 className="text-2xl font-bold mb-2">{plant.name}</h1>
              <p className="text-gray-500 dark:text-gray-400 italic mb-4">{plant.scientificName}</p>

              <div className="mb-4 flex flex-wrap gap-2">
                <span className="inline-block bg-emerald-100 text-emerald-800 text-xs px-2 py-1 rounded-full">
                  {plant.family}
                </span>
                {plant.difficulty && (
                  <span className={`inline-block text-xs px-2 py-1 rounded-full ${
                    plant.difficulty === "Easy" ? "bg-green-100 text-green-800" :
                    plant.difficulty === "Moderate" ? "bg-yellow-100 text-yellow-800" :
                    "bg-red-100 text-red-800"
                  }`}>
                    {plant.difficulty}
                  </span>
                )}
                {plant.growthRate && (
                  <span className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                    {plant.growthRate} Growth
                  </span>
                )}
                {plant.toxicity && (
                  <span className="inline-block bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">
                    {plant.toxicity}
                  </span>
                )}
              </div>

              {/* Show identification confidence if this is from AI identification */}
              {identificationResult && (identificationResult as any).confidence && (
                <div className="mb-4 p-3 bg-emerald-50 dark:bg-emerald-900/20 border border-emerald-200 dark:border-emerald-800 rounded-lg">
                  <div className="flex items-center">
                    <svg className="w-4 h-4 text-emerald-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span className="text-emerald-800 dark:text-emerald-300 text-xs font-medium">
                      AI Identification: {Math.round((identificationResult as any).confidence * 100)}% confidence
                    </span>
                  </div>
                </div>
              )}

              <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
                {plant.description}
              </p>

              {plant.size && (
                <div className="text-sm text-gray-500 dark:text-gray-400">
                  <strong>Mature Size:</strong> {plant.size}
                </div>
              )}
            </div>
          </div>

          <div className="border-t border-gray-200 dark:border-gray-700">
            <div className="flex border-b border-gray-200 dark:border-gray-700">
              <button
                className={`flex-1 py-3 px-4 text-center font-medium ${
                  activeTab === "care"
                    ? "text-emerald-600 border-b-2 border-emerald-600"
                    : "text-gray-500 hover:text-emerald-600"
                }`}
                onClick={() => setActiveTab("care")}
              >
                Care Guide
              </button>
              <button
                className={`flex-1 py-3 px-4 text-center font-medium ${
                  activeTab === "tips"
                    ? "text-emerald-600 border-b-2 border-emerald-600"
                    : "text-gray-500 hover:text-emerald-600"
                }`}
                onClick={() => setActiveTab("tips")}
              >
                Growing Tips
              </button>
            </div>

            <div className="p-6">
              {activeTab === "care" && (
                <div className="space-y-4">
                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-emerald-600" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-medium">Light</h3>
                      <p className="text-gray-600 dark:text-gray-300 text-sm">{plant.care.light}</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-600" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M5.293 9.707a1 1 0 010-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 01-1.414 1.414L11 7.414V15a1 1 0 11-2 0V7.414L6.707 9.707a1 1 0 01-1.414 0z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-medium">Water</h3>
                      <p className="text-gray-600 dark:text-gray-300 text-sm">{plant.care.water}</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-8 h-8 bg-yellow-100 rounded-full flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-yellow-600" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M5 2a1 1 0 011 1v1h1a1 1 0 010 2H6v1a1 1 0 01-2 0V6H3a1 1 0 010-2h1V3a1 1 0 011-1zm0 10a1 1 0 011 1v1h1a1 1 0 110 2H6v1a1 1 0 11-2 0v-1H3a1 1 0 110-2h1v-1a1 1 0 011-1zM12 2a1 1 0 01.967.744L14.146 7.2 17.5 9.134a1 1 0 010 1.732l-3.354 1.935-1.18 4.455a1 1 0 01-1.933 0L9.854 12.8 6.5 10.866a1 1 0 010-1.732l3.354-1.935 1.18-4.455A1 1 0 0112 2z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-medium">Temperature</h3>
                      <p className="text-gray-600 dark:text-gray-300 text-sm">{plant.care.temperature}</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-purple-600" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M5.5 16a3.5 3.5 0 01-.369-6.98 4 4 0 117.753-1.977A4.5 4.5 0 1113.5 16h-8z" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-medium">Humidity</h3>
                      <p className="text-gray-600 dark:text-gray-300 text-sm">{plant.care.humidity}</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-8 h-8 bg-amber-100 rounded-full flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-amber-600" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M2 9.5A3.5 3.5 0 005.5 13H9v2.586l-1.293-1.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 15.586V13h2.5a4.5 4.5 0 10-.616-8.958 4.002 4.002 0 10-7.753 1.977A3.5 3.5 0 002 9.5zm9 3.5H9V8a1 1 0 012 0v5z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-medium">Soil</h3>
                      <p className="text-gray-600 dark:text-gray-300 text-sm">{plant.care.soil}</p>
                    </div>
                  </div>

                  <div className="flex items-start">
                    <div className="flex-shrink-0 w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-600" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M5 2a2 2 0 00-2 2v14l3.5-2 3.5 2 3.5-2 3.5 2V4a2 2 0 00-2-2H5zm4.707 3.707a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L8.414 9H10a3 3 0 013 3v1a1 1 0 102 0v-1a5 5 0 00-5-5H8.414l1.293-1.293z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div>
                      <h3 className="font-medium">Fertilizer</h3>
                      <p className="text-gray-600 dark:text-gray-300 text-sm">{plant.care.fertilizer}</p>
                    </div>
                  </div>
                </div>
              )}

              {activeTab === "tips" && (
                <div className="space-y-4">
                  <div className="bg-emerald-50 dark:bg-emerald-900/20 p-4 rounded-lg">
                    <h3 className="font-medium text-emerald-700 dark:text-emerald-300 mb-2">Light Requirements</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      This plant prefers {plant.care.light.toLowerCase()}. Position it accordingly and rotate occasionally for even growth.
                      {plant.care.light.includes("bright") && " Avoid direct sunlight which can burn the leaves."}
                      {plant.care.light.includes("low") && " This makes it perfect for rooms with limited natural light."}
                    </p>
                  </div>

                  <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                    <h3 className="font-medium text-blue-700 dark:text-blue-300 mb-2">Watering Guidelines</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      {plant.care.water} Check the soil moisture regularly and adjust watering frequency based on the season.
                      {plant.care.humidity.includes("high") && " Increase humidity around the plant with a humidifier or pebble tray."}
                    </p>
                  </div>

                  <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
                    <h3 className="font-medium text-purple-700 dark:text-purple-300 mb-2">Growth & Care</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      {plant.difficulty === "Easy" && "This is a beginner-friendly plant that's very forgiving. "}
                      {plant.difficulty === "Moderate" && "This plant requires some attention but is manageable with basic care. "}
                      {plant.difficulty === "Difficult" && "This plant needs careful attention and specific conditions. "}
                      {plant.growthRate && `Expect ${plant.growthRate.toLowerCase()} growth. `}
                      Regular feeding during the growing season will promote healthy development.
                    </p>
                  </div>

                  <div className="bg-amber-50 dark:bg-amber-900/20 p-4 rounded-lg">
                    <h3 className="font-medium text-amber-700 dark:text-amber-300 mb-2">Temperature & Environment</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      Maintain temperatures between {plant.care.temperature}.
                      {plant.care.humidity.includes("high") && " This plant loves high humidity - consider grouping with other plants or using a humidifier."}
                      {plant.care.humidity.includes("low") && " This plant tolerates low humidity, making it perfect for most homes."}
                      {plant.toxicity && (
                        <span className="block mt-2 text-red-600 font-medium">
                          ⚠️ {plant.toxicity}
                        </span>
                      )}
                    </p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Sources section for AI-identified plants */}
          {identificationResult && (identificationResult as any).sources && (identificationResult as any).sources.length > 0 && (
            <div className="p-6 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600">
              <h3 className="font-medium text-gray-800 dark:text-gray-200 mb-3">Information Sources</h3>
              <div className="space-y-2">
                {(identificationResult as any).sources.map((source: string, index: number) => (
                  <a
                    key={index}
                    href={source}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="block text-sm text-emerald-600 hover:text-emerald-700 dark:text-emerald-400 dark:hover:text-emerald-300 hover:underline"
                  >
                    {source}
                  </a>
                ))}
              </div>
            </div>
          )}

          <div className="p-6 bg-gray-50 dark:bg-gray-700 border-t border-gray-200 dark:border-gray-600">
            <div className="flex justify-between items-center">
              <button className="bg-emerald-600 hover:bg-emerald-700 text-white font-bold py-2 px-4 rounded-lg transition-colors">
                Add to My Garden
              </button>

              <div className="flex space-x-2">
                <button className="bg-gray-200 hover:bg-gray-300 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-700 dark:text-white p-2 rounded-full transition-colors">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M15 8a3 3 0 10-2.977-2.63l-4.94 2.47a3 3 0 100 4.319l4.94 2.47a3 3 0 10.895-1.789l-4.94-2.47a3.027 3.027 0 000-.74l4.94-2.47C13.456 7.68 14.19 8 15 8z" />
                  </svg>
                </button>
                <button className="bg-gray-200 hover:bg-gray-300 dark:bg-gray-600 dark:hover:bg-gray-500 text-gray-700 dark:text-white p-2 rounded-full transition-colors">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M5 4a2 2 0 012-2h6a2 2 0 012 2v14l-5-2.5L5 18V4z" />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
