import { Plant, plantDatabase } from "../data/plants";

// Extended plant interface that includes identification result data
export interface SavedPlantData extends Plant {
  // Original identification data
  identificationResult?: {
    name: string;
    scientificName: string;
    commonNames: string[];
    confidence: number;
    description?: string;
    image?: string;
    taxonomy?: {
      family?: string;
      genus?: string;
      class?: string;
    };
    careGuide: {
      light: string;
      water: string;
      humidity: string;
      temperature: string;
      soil: string;
      fertilizer: string;
      repotting: string;
      commonIssues: string[];
      tips: string[];
      customSoilMix: {
        description: string;
        totalVolume: string;
        bucketMeasurements: Array<{
          material: string;
          buckets: number;
          liters: number;
          purpose: string;
        }>;
        layering: {
          bottom: string[];
          middle: string[];
          top: string[];
        };
        preparation: string[];
        notes: string[];
      };
    };
    topMatches?: Array<{
      name: string;
      scientificName: string;
      confidence: number;
      image?: string;
    }>;
  };
}

// Key for localStorage
const CUSTOM_PLANTS_KEY = "gardenpal_custom_plants";
const REMOVED_PLANTS_KEY = "gardenpal_removed_plants";

// Get custom plants from localStorage
export const getCustomPlants = (): Record<string, SavedPlantData> => {
  if (typeof window === "undefined") return {};

  try {
    const stored = localStorage.getItem(CUSTOM_PLANTS_KEY);
    return stored ? JSON.parse(stored) : {};
  } catch {
    return {};
  }
};

// Get removed plant IDs from localStorage
export const getRemovedPlantIds = (): Set<string> => {
  if (typeof window === "undefined") return new Set();

  try {
    const stored = localStorage.getItem(REMOVED_PLANTS_KEY);
    return new Set(stored ? JSON.parse(stored) : []);
  } catch {
    return new Set();
  }
};

// Save custom plants to localStorage
export const saveCustomPlants = (plants: Record<string, SavedPlantData>): void => {
  if (typeof window === "undefined") return;

  try {
    localStorage.setItem(CUSTOM_PLANTS_KEY, JSON.stringify(plants));
  } catch (error) {
    console.error("Failed to save custom plants:", error);
  }
};

// Save removed plant IDs to localStorage
export const saveRemovedPlantIds = (removedIds: Set<string>): void => {
  if (typeof window === "undefined") return;

  try {
    localStorage.setItem(REMOVED_PLANTS_KEY, JSON.stringify(Array.from(removedIds)));
  } catch (error) {
    console.error("Failed to save removed plants:", error);
  }
};

// Extended Plant interface with ID
export interface PlantWithId extends SavedPlantData {
  id: string;
}

// Get all plants (default + custom - removed) with their IDs
export const getAllManagedPlants = (): PlantWithId[] => {
  const customPlants = getCustomPlants();
  const removedIds = getRemovedPlantIds();

  // Combine default plants and custom plants
  const allPlants: Record<string, SavedPlantData> = {
    ...Object.fromEntries(
      Object.entries(plantDatabase).map(([id, plant]) => [id, plant as SavedPlantData])
    ),
    ...customPlants
  };

  // Filter out removed plants and add IDs
  return Object.entries(allPlants)
    .filter(([id]) => !removedIds.has(id))
    .map(([id, plant]) => ({ ...plant, id }));
};

// Add a custom plant
export const addCustomPlant = (id: string, plant: SavedPlantData): void => {
  const customPlants = getCustomPlants();
  customPlants[id] = plant;
  saveCustomPlants(customPlants);

  // Also remove from removed list if it was there
  const removedIds = getRemovedPlantIds();
  if (removedIds.has(id)) {
    removedIds.delete(id);
    saveRemovedPlantIds(removedIds);
  }
};

// Remove a plant (mark as removed)
export const removePlant = (id: string): void => {
  const removedIds = getRemovedPlantIds();
  removedIds.add(id);
  saveRemovedPlantIds(removedIds);
};

// Restore a removed plant
export const restorePlant = (id: string): void => {
  const removedIds = getRemovedPlantIds();
  removedIds.delete(id);
  saveRemovedPlantIds(removedIds);
};

// Check if a plant is removable (custom plants can be permanently deleted, default plants are just hidden)
export const isCustomPlant = (id: string): boolean => {
  const customPlants = getCustomPlants();
  return id in customPlants;
};

// Permanently delete a custom plant
export const deleteCustomPlant = (id: string): void => {
  const customPlants = getCustomPlants();
  delete customPlants[id];
  saveCustomPlants(customPlants);

  // Also remove from removed list
  const removedIds = getRemovedPlantIds();
  removedIds.delete(id);
  saveRemovedPlantIds(removedIds);
};

// Get plant by ID (including custom plants)
export const getManagedPlantById = (id: string): SavedPlantData | undefined => {
  const customPlants = getCustomPlants();
  const removedIds = getRemovedPlantIds();

  // Check if plant is removed
  if (removedIds.has(id)) {
    return undefined;
  }

  // Check custom plants first, then default plants
  return customPlants[id] || (plantDatabase[id] as SavedPlantData);
};

// Generate a unique ID for a plant
export const generatePlantId = (plantName: string): string => {
  const baseId = plantName.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
  const customPlants = getCustomPlants();
  const allPlants = { ...plantDatabase, ...customPlants };

  let id = baseId;
  let counter = 1;

  while (id in allPlants) {
    id = `${baseId}-${counter}`;
    counter++;
  }

  return id;
};

// Export/Import functionality for syncing between devices
export interface PlantLibraryExport {
  customPlants: Record<string, SavedPlantData>;
  removedPlantIds: string[];
  exportDate: string;
  version: string;
}

// Export plant library data
export const exportPlantLibrary = (): PlantLibraryExport => {
  const customPlants = getCustomPlants();
  const removedIds = getRemovedPlantIds();

  return {
    customPlants,
    removedPlantIds: Array.from(removedIds),
    exportDate: new Date().toISOString(),
    version: "1.0"
  };
};

// Import plant library data
export const importPlantLibrary = (data: PlantLibraryExport, mergeMode: 'replace' | 'merge' = 'merge'): boolean => {
  try {
    if (mergeMode === 'replace') {
      // Replace all data
      saveCustomPlants(data.customPlants);
      saveRemovedPlantIds(new Set(data.removedPlantIds));
    } else {
      // Merge with existing data
      const existingCustomPlants = getCustomPlants();
      const existingRemovedIds = getRemovedPlantIds();

      // Merge custom plants (imported plants take precedence)
      const mergedCustomPlants = { ...existingCustomPlants, ...data.customPlants };
      saveCustomPlants(mergedCustomPlants);

      // Merge removed IDs
      const mergedRemovedIds = new Set([...existingRemovedIds, ...data.removedPlantIds]);
      saveRemovedPlantIds(mergedRemovedIds);
    }

    return true;
  } catch (error) {
    console.error("Failed to import plant library:", error);
    return false;
  }
};

// Generate downloadable JSON file
export const downloadPlantLibrary = (): void => {
  const data = exportPlantLibrary();
  const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
  const url = URL.createObjectURL(blob);

  const link = document.createElement('a');
  link.href = url;
  link.download = `plantpal-library-${new Date().toISOString().split('T')[0]}.json`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);

  URL.revokeObjectURL(url);
};

// Get library statistics
export const getLibraryStats = () => {
  const customPlants = getCustomPlants();
  const removedIds = getRemovedPlantIds();
  const allPlants = getAllManagedPlants();

  return {
    totalPlants: allPlants.length,
    customPlants: Object.keys(customPlants).length,
    defaultPlants: Object.keys(plantDatabase).length - removedIds.size,
    removedPlants: removedIds.size,
    identifiedPlants: Object.values(customPlants).filter(plant => plant.identificationResult).length
  };
};
