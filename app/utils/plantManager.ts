import { Plant, plantDatabase } from "../data/plants";

// Extended plant interface that includes identification result data
export interface SavedPlantData extends Plant {
  // Original identification data
  identificationResult?: {
    name: string;
    scientificName: string;
    commonNames: string[];
    confidence: number;
    description?: string;
    image?: string;
    taxonomy?: {
      family?: string;
      genus?: string;
      class?: string;
    };
    careGuide: {
      light: string;
      water: string;
      humidity: string;
      temperature: string;
      soil: string;
      fertilizer: string;
      repotting: string;
      commonIssues: string[];
      tips: string[];
      customSoilMix: {
        description: string;
        totalVolume: string;
        bucketMeasurements: Array<{
          material: string;
          buckets: number;
          liters: number;
          purpose: string;
        }>;
        layering: {
          bottom: string[];
          middle: string[];
          top: string[];
        };
        preparation: string[];
        notes: string[];
      };
    };
    topMatches?: Array<{
      name: string;
      scientificName: string;
      confidence: number;
      image?: string;
    }>;
  };
}

// Key for localStorage
const CUSTOM_PLANTS_KEY = "gardenpal_custom_plants";
const REMOVED_PLANTS_KEY = "gardenpal_removed_plants";

// Get custom plants from localStorage
export const getCustomPlants = (): Record<string, SavedPlantData> => {
  if (typeof window === "undefined") return {};

  try {
    const stored = localStorage.getItem(CUSTOM_PLANTS_KEY);
    return stored ? JSON.parse(stored) : {};
  } catch {
    return {};
  }
};

// Get removed plant IDs from localStorage
export const getRemovedPlantIds = (): Set<string> => {
  if (typeof window === "undefined") return new Set();

  try {
    const stored = localStorage.getItem(REMOVED_PLANTS_KEY);
    return new Set(stored ? JSON.parse(stored) : []);
  } catch {
    return new Set();
  }
};

// Save custom plants to localStorage
export const saveCustomPlants = (plants: Record<string, SavedPlantData>): void => {
  if (typeof window === "undefined") return;

  try {
    localStorage.setItem(CUSTOM_PLANTS_KEY, JSON.stringify(plants));
  } catch (error) {
    console.error("Failed to save custom plants:", error);
  }
};

// Save removed plant IDs to localStorage
export const saveRemovedPlantIds = (removedIds: Set<string>): void => {
  if (typeof window === "undefined") return;

  try {
    localStorage.setItem(REMOVED_PLANTS_KEY, JSON.stringify(Array.from(removedIds)));
  } catch (error) {
    console.error("Failed to save removed plants:", error);
  }
};

// Extended Plant interface with ID
export interface PlantWithId extends SavedPlantData {
  id: string;
}

// Get all plants (default + custom - removed) with their IDs
export const getAllManagedPlants = (): PlantWithId[] => {
  const customPlants = getCustomPlants();
  const removedIds = getRemovedPlantIds();

  // Combine default plants and custom plants
  const allPlants: Record<string, SavedPlantData> = {
    ...Object.fromEntries(
      Object.entries(plantDatabase).map(([id, plant]) => [id, plant as SavedPlantData])
    ),
    ...customPlants
  };

  // Filter out removed plants and add IDs
  return Object.entries(allPlants)
    .filter(([id]) => !removedIds.has(id))
    .map(([id, plant]) => ({ ...plant, id }));
};

// Add a custom plant
export const addCustomPlant = (id: string, plant: SavedPlantData): void => {
  const customPlants = getCustomPlants();
  customPlants[id] = plant;
  saveCustomPlants(customPlants);

  // Also remove from removed list if it was there
  const removedIds = getRemovedPlantIds();
  if (removedIds.has(id)) {
    removedIds.delete(id);
    saveRemovedPlantIds(removedIds);
  }
};

// Remove a plant (mark as removed)
export const removePlant = (id: string): void => {
  const removedIds = getRemovedPlantIds();
  removedIds.add(id);
  saveRemovedPlantIds(removedIds);
};

// Restore a removed plant
export const restorePlant = (id: string): void => {
  const removedIds = getRemovedPlantIds();
  removedIds.delete(id);
  saveRemovedPlantIds(removedIds);
};

// Check if a plant is removable (custom plants can be permanently deleted, default plants are just hidden)
export const isCustomPlant = (id: string): boolean => {
  const customPlants = getCustomPlants();
  return id in customPlants;
};

// Permanently delete a custom plant
export const deleteCustomPlant = (id: string): void => {
  const customPlants = getCustomPlants();
  delete customPlants[id];
  saveCustomPlants(customPlants);

  // Also remove from removed list
  const removedIds = getRemovedPlantIds();
  removedIds.delete(id);
  saveRemovedPlantIds(removedIds);
};

// Get plant by ID (including custom plants)
export const getManagedPlantById = (id: string): SavedPlantData | undefined => {
  const customPlants = getCustomPlants();
  const removedIds = getRemovedPlantIds();

  // Check if plant is removed
  if (removedIds.has(id)) {
    return undefined;
  }

  // Check custom plants first, then default plants
  return customPlants[id] || (plantDatabase[id] as SavedPlantData);
};

// Generate a unique ID for a plant
export const generatePlantId = (plantName: string): string => {
  const baseId = plantName.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '');
  const customPlants = getCustomPlants();
  const allPlants = { ...plantDatabase, ...customPlants };

  let id = baseId;
  let counter = 1;

  while (id in allPlants) {
    id = `${baseId}-${counter}`;
    counter++;
  }

  return id;
};
