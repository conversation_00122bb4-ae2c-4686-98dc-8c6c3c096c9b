import { Link, useRouteError, isRouteErrorResponse } from "@remix-run/react";

export function ErrorBoundary() {
  const error = useRouteError();

  if (isRouteErrorResponse(error)) {
    return (
      <div className="min-h-screen flex flex-col">
        <header className="bg-emerald-600 text-white p-4 shadow-md">
          <div className="container mx-auto flex justify-between items-center">
            <Link to="/" className="text-2xl font-bold">GardenPal</Link>
          </div>
        </header>
        
        <main className="flex-grow container mx-auto p-4 md:p-6 flex items-center justify-center">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 max-w-md w-full text-center">
            <div className="text-6xl font-bold text-emerald-600 mb-4">
              {error.status}
            </div>
            <h1 className="text-2xl font-bold mb-4">
              {error.status === 404 ? "Page Not Found" : "Something went wrong"}
            </h1>
            <p className="text-gray-600 dark:text-gray-300 mb-6">
              {error.status === 404 
                ? "The page you're looking for doesn't exist or may have been moved."
                : error.statusText || "An unexpected error occurred."
              }
            </p>
            <div className="space-y-3">
              <Link 
                to="/"
                className="block w-full bg-emerald-600 hover:bg-emerald-700 text-white font-bold py-3 px-4 rounded-lg transition-colors"
              >
                Go Home
              </Link>
              <Link 
                to="/library"
                className="block w-full bg-emerald-100 hover:bg-emerald-200 text-emerald-800 font-bold py-3 px-4 rounded-lg transition-colors"
              >
                Browse Plants
              </Link>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex flex-col">
      <header className="bg-emerald-600 text-white p-4 shadow-md">
        <div className="container mx-auto flex justify-between items-center">
          <Link to="/" className="text-2xl font-bold">GardenPal</Link>
        </div>
      </header>
      
      <main className="flex-grow container mx-auto p-4 md:p-6 flex items-center justify-center">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 max-w-md w-full text-center">
          <div className="text-red-500 mb-4">
            <svg className="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <h1 className="text-2xl font-bold mb-4">Oops! Something went wrong</h1>
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            We encountered an unexpected error. Please try again or go back to the home page.
          </p>
          <div className="space-y-3">
            <button 
              onClick={() => window.location.reload()}
              className="block w-full bg-emerald-600 hover:bg-emerald-700 text-white font-bold py-3 px-4 rounded-lg transition-colors"
            >
              Try Again
            </button>
            <Link 
              to="/"
              className="block w-full bg-emerald-100 hover:bg-emerald-200 text-emerald-800 font-bold py-3 px-4 rounded-lg transition-colors"
            >
              Go Home
            </Link>
          </div>
        </div>
      </main>
    </div>
  );
}
