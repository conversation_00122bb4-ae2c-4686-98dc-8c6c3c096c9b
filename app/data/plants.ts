export interface PlantCare {
  light: string;
  water: string;
  humidity: string;
  temperature: string;
  soil: string;
  fertilizer: string;
}

export interface Plant {
  name: string;
  commonName: string;
  scientificName: string;
  family: string;
  image: string;
  description: string;
  care: PlantCare;
  toxicity?: string;
  difficulty?: "Easy" | "Moderate" | "Difficult";
  growthRate?: "Slow" | "Moderate" | "Fast";
  size?: string;
}

export const plantDatabase: Record<string, Plant> = {
  "monstera-deliciosa": {
    name: "Monstera Deliciosa",
    commonName: "Swiss Cheese Plant",
    scientificName: "Monstera deliciosa",
    family: "Araceae",
    image: "https://images.unsplash.com/photo-1614594975525-e45190c55d0b?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=764&q=80",
    description: "The Monstera deliciosa is a species of flowering plant native to tropical forests of southern Mexico, south to Panama. It has been introduced to many tropical areas, and has become a mildly invasive species in Hawaii, Seychelles, Ascension Island and the Society Islands.",
    care: {
      light: "Bright indirect light",
      water: "Allow soil to dry out between waterings",
      humidity: "Moderate to high",
      temperature: "65-85°F (18-29°C)",
      soil: "Well-draining potting mix",
      fertilizer: "Monthly during growing season"
    },
    toxicity: "Toxic to pets if ingested",
    difficulty: "Easy",
    growthRate: "Fast",
    size: "Large (6-8 feet indoors)"
  },
  "pothos": {
    name: "Pothos",
    commonName: "Devil's Ivy",
    scientificName: "Epipremnum aureum",
    family: "Araceae",
    image: "https://images.unsplash.com/photo-1572688484438-313a6e50c333?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1470&q=80",
    description: "Pothos is an evergreen vine with thick, waxy, green, heart-shaped leaves with splashes of yellow. As a houseplant, it is commonly grown as a hanging plant.",
    care: {
      light: "Low to bright indirect light",
      water: "Allow soil to dry out between waterings",
      humidity: "Any",
      temperature: "65-85°F (18-29°C)",
      soil: "Well-draining potting mix",
      fertilizer: "Monthly during growing season"
    },
    toxicity: "Toxic to pets if ingested",
    difficulty: "Easy",
    growthRate: "Fast",
    size: "Trailing (3-6 feet)"
  },
  "snake-plant": {
    name: "Snake Plant",
    commonName: "Mother-in-Law's Tongue",
    scientificName: "Sansevieria trifasciata",
    family: "Asparagaceae",
    image: "https://images.unsplash.com/photo-1593691509543-c55fb32d8de5?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1470&q=80",
    description: "Snake plants are evergreen perennials that can grow anywhere from 8 inches to 12 feet high. Their sword-like leaves are approximately two feet long. The foliage is stiff, broad, and upright, in a dark green color variegated with white and yellow striping.",
    care: {
      light: "Low to bright indirect light",
      water: "Water sparingly, every 2-3 weeks",
      humidity: "Low to moderate",
      temperature: "60-80°F (15-27°C)",
      soil: "Well-draining cactus mix",
      fertilizer: "Rarely needed, once in spring"
    },
    difficulty: "Easy",
    growthRate: "Slow",
    size: "Medium (2-4 feet)"
  },
  "fiddle-leaf-fig": {
    name: "Fiddle Leaf Fig",
    commonName: "Fiddle Leaf Fig",
    scientificName: "Ficus lyrata",
    family: "Moraceae",
    image: "https://images.unsplash.com/photo-1586093248292-4e6636ce8b97?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1470&q=80",
    description: "The fiddle leaf fig is native to western Africa and grows naturally in lowland tropical rainforest. It can grow up to 50 feet tall in its native habitat, but typically reaches 6-10 feet as a houseplant.",
    care: {
      light: "Bright indirect light",
      water: "Water when top inch of soil is dry",
      humidity: "Moderate to high",
      temperature: "65-75°F (18-24°C)",
      soil: "Well-draining potting mix",
      fertilizer: "Monthly during growing season"
    },
    toxicity: "Toxic to pets if ingested",
    difficulty: "Moderate",
    growthRate: "Moderate",
    size: "Large (6-10 feet indoors)"
  },
  "rubber-plant": {
    name: "Rubber Plant",
    commonName: "Rubber Tree",
    scientificName: "Ficus elastica",
    family: "Moraceae",
    image: "https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1470&q=80",
    description: "The rubber plant is a popular ornamental plant from the Ficus genus. In its native jungle habitat in India and Southeast Asia, this plant can grow to be over 100 feet tall with a trunk diameter of more than 6 feet.",
    care: {
      light: "Bright indirect light",
      water: "Water when top inch of soil is dry",
      humidity: "Moderate",
      temperature: "65-80°F (18-27°C)",
      soil: "Well-draining potting mix",
      fertilizer: "Monthly during growing season"
    },
    toxicity: "Toxic to pets if ingested",
    difficulty: "Easy",
    growthRate: "Moderate",
    size: "Large (6-8 feet indoors)"
  },
  "peace-lily": {
    name: "Peace Lily",
    commonName: "Peace Lily",
    scientificName: "Spathiphyllum wallisii",
    family: "Araceae",
    image: "https://images.unsplash.com/photo-1583160247711-2191776b4b91?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1470&q=80",
    description: "Peace lilies are tropical plants that thrive in the warm, humid conditions of the rainforest floor. They're known for their beautiful white flowers and ability to purify indoor air.",
    care: {
      light: "Low to medium indirect light",
      water: "Keep soil consistently moist",
      humidity: "High",
      temperature: "65-80°F (18-27°C)",
      soil: "Well-draining potting mix",
      fertilizer: "Monthly during growing season"
    },
    toxicity: "Toxic to pets if ingested",
    difficulty: "Easy",
    growthRate: "Moderate",
    size: "Medium (1-3 feet)"
  }
};

export const getAllPlants = (): Plant[] => {
  return Object.values(plantDatabase);
};

export const getPlantById = (id: string): Plant | undefined => {
  return plantDatabase[id];
};

export const getPlantsByDifficulty = (difficulty: Plant["difficulty"]): Plant[] => {
  return getAllPlants().filter(plant => plant.difficulty === difficulty);
};
