import { type AnyNode, Document, type ParentNode } from 'domhandler';
import type { InternalOptions } from './options.js';
/**
 * Get the parse function with options.
 *
 * @param parser - The parser function.
 * @returns The parse function with options.
 */
export declare function getParse(parser: (content: string, options: InternalOptions, isDocument: boolean, context: ParentNode | null) => Document): (content: string | Document | AnyNode | AnyNode[] | <PERSON><PERSON><PERSON>, options: InternalOptions, isDocument: boolean, context: ParentNode | null) => Document;
/**
 * Update the dom structure, for one changed layer.
 *
 * @param newChilds - The new children.
 * @param parent - The new parent.
 * @returns The parent node.
 */
export declare function update(newChilds: AnyNode[] | AnyNode, parent: ParentNode | null): ParentNode | null;
//# sourceMappingURL=parse.d.ts.map