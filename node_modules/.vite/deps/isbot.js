import "./chunk-VRMXEQCD.js";

// node_modules/isbot/index.mjs
var fullPattern = " daum[ /]| deusu/| yadirectfetcher|(?:^| )site|(?:^|[^g])news|(?<! (?:channel/|google/))google(?!(app|/google| pixel))|(?<! cu)bot(?:[^\\w]|_|$)|(?<! ya(?:yandex)?)search|(?<!(?:lib))http|(?<![hg]m)score|@[a-z]|\\(at\\)[a-z]|\\[at\\][a-z]|^12345|^<|^[\\w \\.\\-\\(?:\\):]+(?:/v?\\d+(\\.\\d+)?(?:\\.\\d{1,10})?)?(?:,|$)|^[^ ]{50,}$|^active|^ad muncher|^amaya|^anglesharp/|^avsdevicesdk/|^bidtellect/|^biglotron|^bot|^btwebclient/|^clamav[ /]|^client/|^cobweb/|^coccoc|^custom|^ddg[_-]android|^discourse|^dispatch/\\d|^downcast/|^duckduckgo|^facebook|^fdm[ /]\\d|^getright/|^gozilla/|^hatena|^hobbit|^hotzonu|^hwcdn/|^jeode/|^jetty/|^jigsaw|^linkdex|^metauri|^microsoft bits|^movabletype|^mozilla/\\d\\.\\d \\(compatible;?\\)$|^mozilla/\\d\\.\\d \\w*$|^navermailapp|^netsurf|^nuclei|^offline explorer|^php|^postman|^postrank|^python|^rank|^read|^reed|^rest|^serf|^snapchat|^space bison|^svn|^swcd |^taringa|^thumbor/|^tumblr/|^user-agent:|^valid|^venus/fedoraplanet|^w3c|^webbandit/|^webcopier|^wget|^whatsapp|^xenu link sleuth|^yahoo|^yandex|^zdm/\\d|^zoom marketplace/|^{{.*}}$|adbeat\\.com|appinsights|archive|ask jeeves/teoma|bit\\.ly/|bluecoat drtr|browsex|burpcollaborator|capture|catch|check|chrome-lighthouse|chromeframe|classifier|cloud|crawl|cryptoapi|dareboost|datanyze|dataprovider|dejaclick|dmbrowser|download|evc-batch/|feed|firephp|freesafeip|gomezagent|headless|httrack|hubspot marketing grader|hydra|ibisbrowser|images|inspect|iplabel|ips-agent|java(?!;)|library|mail\\.ru/|manager|monitor|neustar wpm|nutch|offbyone|optimize|pageburst|parser|perl|phantom|pingdom|powermarks|preview|proxy|ptst[ /]\\d|reader|reputation|resolver|retriever|rexx;|rigor|robot|rss|scan|scrape|server|sogou|sparkler/|speedcurve|spider|splash|statuscake|stumbleupon\\.com|supercleaner|synapse|synthetic|torrent|trace|transcoder|twingly recon|url|virtuoso|wappalyzer|webglance|webkit2png|whatcms/|wordpress|zgrab";
var regularExpression = new RegExp(" daum[ /]| deusu\\/| yadirectfetcher|(?:^| )site|(?:^|[^g])news|(?<! (?:channel\\/|google\\/))google(?!(app|\\/google| pixel))|(?<! cu)bot(?:[^\\w]|_|$)|(?<! ya(?:yandex)?)search|(?<!(?:lib))http|(?<![hg]m)score|@[a-z]|\\(at\\)[a-z]|\\[at\\][a-z]|^12345|^<|^[\\w \\.\\-\\(?:\\):]+(?:\\/v?\\d+(\\.\\d+)?(?:\\.\\d{1,10})?)?(?:,|$)|^[^ ]{50,}$|^active|^ad muncher|^amaya|^anglesharp\\/|^avsdevicesdk\\/|^bidtellect\\/|^biglotron|^bot|^btwebclient\\/|^clamav[ /]|^client\\/|^cobweb\\/|^coccoc|^custom|^ddg[_-]android|^discourse|^dispatch\\/\\d|^downcast\\/|^duckduckgo|^facebook|^fdm[ /]\\d|^getright\\/|^gozilla\\/|^hatena|^hobbit|^hotzonu|^hwcdn\\/|^jeode\\/|^jetty\\/|^jigsaw|^linkdex|^metauri|^microsoft bits|^movabletype|^mozilla\\/\\d\\.\\d \\(compatible;?\\)$|^mozilla\\/\\d\\.\\d \\w*$|^navermailapp|^netsurf|^nuclei|^offline explorer|^php|^postman|^postrank|^python|^rank|^read|^reed|^rest|^serf|^snapchat|^space bison|^svn|^swcd |^taringa|^thumbor\\/|^tumblr\\/|^user-agent:|^valid|^venus\\/fedoraplanet|^w3c|^webbandit\\/|^webcopier|^wget|^whatsapp|^xenu link sleuth|^yahoo|^yandex|^zdm\\/\\d|^zoom marketplace\\/|^{{.*}}$|adbeat\\.com|appinsights|archive|ask jeeves\\/teoma|bit\\.ly\\/|bluecoat drtr|browsex|burpcollaborator|capture|catch|check|chrome-lighthouse|chromeframe|classifier|cloud|crawl|cryptoapi|dareboost|datanyze|dataprovider|dejaclick|dmbrowser|download|evc-batch\\/|feed|firephp|freesafeip|gomezagent|headless|httrack|hubspot marketing grader|hydra|ibisbrowser|images|inspect|iplabel|ips-agent|java(?!;)|library|mail\\.ru\\/|manager|monitor|neustar wpm|nutch|offbyone|optimize|pageburst|parser|perl|phantom|pingdom|powermarks|preview|proxy|ptst[ /]\\d|reader|reputation|resolver|retriever|rexx;|rigor|robot|rss|scan|scrape|server|sogou|sparkler\\/|speedcurve|spider|splash|statuscake|stumbleupon\\.com|supercleaner|synapse|synthetic|torrent|trace|transcoder|twingly recon|url|virtuoso|wappalyzer|webglance|webkit2png|whatcms\\/|wordpress|zgrab", "i");
var patterns_default = [
  " daum[ /]",
  " deusu/",
  " yadirectfetcher",
  "(?:^| )site",
  "(?:^|[^g])news",
  "(?<! (?:channel/|google/))google(?!(app|/google| pixel))",
  "(?<! cu)bot(?:[^\\w]|_|$)",
  "(?<! ya(?:yandex)?)search",
  "(?<!(?:lib))http",
  "(?<![hg]m)score",
  "@[a-z]",
  "\\(at\\)[a-z]",
  "\\[at\\][a-z]",
  "^12345",
  "^<",
  "^[\\w \\.\\-\\(?:\\):]+(?:/v?\\d+(\\.\\d+)?(?:\\.\\d{1,10})?)?(?:,|$)",
  "^[^ ]{50,}$",
  "^active",
  "^ad muncher",
  "^amaya",
  "^anglesharp/",
  "^avsdevicesdk/",
  "^bidtellect/",
  "^biglotron",
  "^bot",
  "^btwebclient/",
  "^clamav[ /]",
  "^client/",
  "^cobweb/",
  "^coccoc",
  "^custom",
  "^ddg[_-]android",
  "^discourse",
  "^dispatch/\\d",
  "^downcast/",
  "^duckduckgo",
  "^facebook",
  "^fdm[ /]\\d",
  "^getright/",
  "^gozilla/",
  "^hatena",
  "^hobbit",
  "^hotzonu",
  "^hwcdn/",
  "^jeode/",
  "^jetty/",
  "^jigsaw",
  "^linkdex",
  "^metauri",
  "^microsoft bits",
  "^movabletype",
  "^mozilla/\\d\\.\\d \\(compatible;?\\)$",
  "^mozilla/\\d\\.\\d \\w*$",
  "^navermailapp",
  "^netsurf",
  "^nuclei",
  "^offline explorer",
  "^php",
  "^postman",
  "^postrank",
  "^python",
  "^rank",
  "^read",
  "^reed",
  "^rest",
  "^serf",
  "^snapchat",
  "^space bison",
  "^svn",
  "^swcd ",
  "^taringa",
  "^thumbor/",
  "^tumblr/",
  "^user-agent:",
  "^valid",
  "^venus/fedoraplanet",
  "^w3c",
  "^webbandit/",
  "^webcopier",
  "^wget",
  "^whatsapp",
  "^xenu link sleuth",
  "^yahoo",
  "^yandex",
  "^zdm/\\d",
  "^zoom marketplace/",
  "^{{.*}}$",
  "adbeat\\.com",
  "appinsights",
  "archive",
  "ask jeeves/teoma",
  "bit\\.ly/",
  "bluecoat drtr",
  "browsex",
  "burpcollaborator",
  "capture",
  "catch",
  "check",
  "chrome-lighthouse",
  "chromeframe",
  "classifier",
  "cloud",
  "crawl",
  "cryptoapi",
  "dareboost",
  "datanyze",
  "dataprovider",
  "dejaclick",
  "dmbrowser",
  "download",
  "evc-batch/",
  "feed",
  "firephp",
  "freesafeip",
  "gomezagent",
  "headless",
  "httrack",
  "hubspot marketing grader",
  "hydra",
  "ibisbrowser",
  "images",
  "inspect",
  "iplabel",
  "ips-agent",
  "java(?!;)",
  "library",
  "mail\\.ru/",
  "manager",
  "monitor",
  "neustar wpm",
  "nutch",
  "offbyone",
  "optimize",
  "pageburst",
  "parser",
  "perl",
  "phantom",
  "pingdom",
  "powermarks",
  "preview",
  "proxy",
  "ptst[ /]\\d",
  "reader",
  "reputation",
  "resolver",
  "retriever",
  "rexx;",
  "rigor",
  "robot",
  "rss",
  "scan",
  "scrape",
  "server",
  "sogou",
  "sparkler/",
  "speedcurve",
  "spider",
  "splash",
  "statuscake",
  "stumbleupon\\.com",
  "supercleaner",
  "synapse",
  "synthetic",
  "torrent",
  "trace",
  "transcoder",
  "twingly recon",
  "url",
  "virtuoso",
  "wappalyzer",
  "webglance",
  "webkit2png",
  "whatcms/",
  "wordpress",
  "zgrab"
];
var naivePattern = /bot|spider|crawl|http|lighthouse/i;
var pattern = regularExpression;
var list = patterns_default;
var isbotNaive = (userAgent) => Boolean(userAgent) && naivePattern.test(userAgent);
var usedPattern;
function isbot(userAgent) {
  if (typeof usedPattern === "undefined") {
    try {
      usedPattern = new RegExp(fullPattern, "i");
    } catch (error) {
      usedPattern = naivePattern;
    }
  }
  return Boolean(userAgent) && usedPattern.test(userAgent);
}
var createIsbot = (customPattern) => (userAgent) => Boolean(userAgent) && customPattern.test(userAgent);
var createIsbotFromList = (list2) => {
  const pattern2 = new RegExp(list2.join("|"), "i");
  return (userAgent) => Boolean(userAgent) && pattern2.test(userAgent);
};
var isbotMatch = (userAgent) => {
  var _a;
  return ((_a = userAgent == null ? void 0 : userAgent.match(pattern)) == null ? void 0 : _a[0]) ?? null;
};
var isbotMatches = (userAgent) => list.map((part) => {
  var _a;
  return (_a = userAgent == null ? void 0 : userAgent.match(new RegExp(part, "i"))) == null ? void 0 : _a[0];
}).filter(Boolean);
var isbotPattern = (userAgent) => userAgent ? list.find((pattern2) => new RegExp(pattern2, "i").test(userAgent)) ?? null : null;
var isbotPatterns = (userAgent) => userAgent ? list.filter((pattern2) => new RegExp(pattern2, "i").test(userAgent)) : [];
export {
  createIsbot,
  createIsbotFromList,
  isbot,
  isbotMatch,
  isbotMatches,
  isbotNaive,
  isbotPattern,
  isbotPatterns,
  list,
  pattern
};
//# sourceMappingURL=isbot.js.map
