{"version": 3, "sources": ["../../isbot/index.mjs"], "sourcesContent": ["// src/pattern.ts\nvar fullPattern = \" daum[ /]| deusu/| yadirectfetcher|(?:^| )site|(?:^|[^g])news|(?<! (?:channel/|google/))google(?!(app|/google| pixel))|(?<! cu)bot(?:[^\\\\w]|_|$)|(?<! ya(?:yandex)?)search|(?<!(?:lib))http|(?<![hg]m)score|@[a-z]|\\\\(at\\\\)[a-z]|\\\\[at\\\\][a-z]|^12345|^<|^[\\\\w \\\\.\\\\-\\\\(?:\\\\):]+(?:/v?\\\\d+(\\\\.\\\\d+)?(?:\\\\.\\\\d{1,10})?)?(?:,|$)|^[^ ]{50,}$|^active|^ad muncher|^amaya|^anglesharp/|^avsdevicesdk/|^bidtellect/|^biglotron|^bot|^btwebclient/|^clamav[ /]|^client/|^cobweb/|^coccoc|^custom|^ddg[_-]android|^discourse|^dispatch/\\\\d|^downcast/|^duckduckgo|^facebook|^fdm[ /]\\\\d|^getright/|^gozilla/|^hatena|^hobbit|^hotzonu|^hwcdn/|^jeode/|^jetty/|^jigsaw|^linkdex|^metauri|^microsoft bits|^movabletype|^mozilla/\\\\d\\\\.\\\\d \\\\(compatible;?\\\\)$|^mozilla/\\\\d\\\\.\\\\d \\\\w*$|^navermailapp|^netsurf|^nuclei|^offline explorer|^php|^postman|^postrank|^python|^rank|^read|^reed|^rest|^serf|^snapchat|^space bison|^svn|^swcd |^taringa|^thumbor/|^tumblr/|^user-agent:|^valid|^venus/fedoraplanet|^w3c|^webbandit/|^webcopier|^wget|^whatsapp|^xenu link sleuth|^yahoo|^yandex|^zdm/\\\\d|^zoom marketplace/|^{{.*}}$|adbeat\\\\.com|appinsights|archive|ask jeeves/teoma|bit\\\\.ly/|bluecoat drtr|browsex|burpcollaborator|capture|catch|check|chrome-lighthouse|chromeframe|classifier|cloud|crawl|cryptoapi|dareboost|datanyze|dataprovider|dejaclick|dmbrowser|download|evc-batch/|feed|firephp|freesafeip|gomezagent|headless|httrack|hubspot marketing grader|hydra|ibisbrowser|images|inspect|iplabel|ips-agent|java(?!;)|library|mail\\\\.ru/|manager|monitor|neustar wpm|nutch|offbyone|optimize|pageburst|parser|perl|phantom|pingdom|powermarks|preview|proxy|ptst[ /]\\\\d|reader|reputation|resolver|retriever|rexx;|rigor|robot|rss|scan|scrape|server|sogou|sparkler/|speedcurve|spider|splash|statuscake|stumbleupon\\\\.com|supercleaner|synapse|synthetic|torrent|trace|transcoder|twingly recon|url|virtuoso|wappalyzer|webglance|webkit2png|whatcms/|wordpress|zgrab\";\nvar regularExpression = / daum[ /]| deusu\\/| yadirectfetcher|(?:^| )site|(?:^|[^g])news|(?<! (?:channel\\/|google\\/))google(?!(app|\\/google| pixel))|(?<! cu)bot(?:[^\\w]|_|$)|(?<! ya(?:yandex)?)search|(?<!(?:lib))http|(?<![hg]m)score|@[a-z]|\\(at\\)[a-z]|\\[at\\][a-z]|^12345|^<|^[\\w \\.\\-\\(?:\\):]+(?:\\/v?\\d+(\\.\\d+)?(?:\\.\\d{1,10})?)?(?:,|$)|^[^ ]{50,}$|^active|^ad muncher|^amaya|^anglesharp\\/|^avsdevicesdk\\/|^bidtellect\\/|^biglotron|^bot|^btwebclient\\/|^clamav[ /]|^client\\/|^cobweb\\/|^coccoc|^custom|^ddg[_-]android|^discourse|^dispatch\\/\\d|^downcast\\/|^duckduckgo|^facebook|^fdm[ /]\\d|^getright\\/|^gozilla\\/|^hatena|^hobbit|^hotzonu|^hwcdn\\/|^jeode\\/|^jetty\\/|^jigsaw|^linkdex|^metauri|^microsoft bits|^movabletype|^mozilla\\/\\d\\.\\d \\(compatible;?\\)$|^mozilla\\/\\d\\.\\d \\w*$|^navermailapp|^netsurf|^nuclei|^offline explorer|^php|^postman|^postrank|^python|^rank|^read|^reed|^rest|^serf|^snapchat|^space bison|^svn|^swcd |^taringa|^thumbor\\/|^tumblr\\/|^user-agent:|^valid|^venus\\/fedoraplanet|^w3c|^webbandit\\/|^webcopier|^wget|^whatsapp|^xenu link sleuth|^yahoo|^yandex|^zdm\\/\\d|^zoom marketplace\\/|^{{.*}}$|adbeat\\.com|appinsights|archive|ask jeeves\\/teoma|bit\\.ly\\/|bluecoat drtr|browsex|burpcollaborator|capture|catch|check|chrome-lighthouse|chromeframe|classifier|cloud|crawl|cryptoapi|dareboost|datanyze|dataprovider|dejaclick|dmbrowser|download|evc-batch\\/|feed|firephp|freesafeip|gomezagent|headless|httrack|hubspot marketing grader|hydra|ibisbrowser|images|inspect|iplabel|ips-agent|java(?!;)|library|mail\\.ru\\/|manager|monitor|neustar wpm|nutch|offbyone|optimize|pageburst|parser|perl|phantom|pingdom|powermarks|preview|proxy|ptst[ /]\\d|reader|reputation|resolver|retriever|rexx;|rigor|robot|rss|scan|scrape|server|sogou|sparkler\\/|speedcurve|spider|splash|statuscake|stumbleupon\\.com|supercleaner|synapse|synthetic|torrent|trace|transcoder|twingly recon|url|virtuoso|wappalyzer|webglance|webkit2png|whatcms\\/|wordpress|zgrab/i;\n\n// src/patterns.json\nvar patterns_default = [\n  \" daum[ /]\",\n  \" deusu/\",\n  \" yadirectfetcher\",\n  \"(?:^| )site\",\n  \"(?:^|[^g])news\",\n  \"(?<! (?:channel/|google/))google(?!(app|/google| pixel))\",\n  \"(?<! cu)bot(?:[^\\\\w]|_|$)\",\n  \"(?<! ya(?:yandex)?)search\",\n  \"(?<!(?:lib))http\",\n  \"(?<![hg]m)score\",\n  \"@[a-z]\",\n  \"\\\\(at\\\\)[a-z]\",\n  \"\\\\[at\\\\][a-z]\",\n  \"^12345\",\n  \"^<\",\n  \"^[\\\\w \\\\.\\\\-\\\\(?:\\\\):]+(?:/v?\\\\d+(\\\\.\\\\d+)?(?:\\\\.\\\\d{1,10})?)?(?:,|$)\",\n  \"^[^ ]{50,}$\",\n  \"^active\",\n  \"^ad muncher\",\n  \"^amaya\",\n  \"^anglesharp/\",\n  \"^avsdevicesdk/\",\n  \"^bidtellect/\",\n  \"^biglotron\",\n  \"^bot\",\n  \"^btwebclient/\",\n  \"^clamav[ /]\",\n  \"^client/\",\n  \"^cobweb/\",\n  \"^coccoc\",\n  \"^custom\",\n  \"^ddg[_-]android\",\n  \"^discourse\",\n  \"^dispatch/\\\\d\",\n  \"^downcast/\",\n  \"^duckduckgo\",\n  \"^facebook\",\n  \"^fdm[ /]\\\\d\",\n  \"^getright/\",\n  \"^gozilla/\",\n  \"^hatena\",\n  \"^hobbit\",\n  \"^hotzonu\",\n  \"^hwcdn/\",\n  \"^jeode/\",\n  \"^jetty/\",\n  \"^jigsaw\",\n  \"^linkdex\",\n  \"^metauri\",\n  \"^microsoft bits\",\n  \"^movabletype\",\n  \"^mozilla/\\\\d\\\\.\\\\d \\\\(compatible;?\\\\)$\",\n  \"^mozilla/\\\\d\\\\.\\\\d \\\\w*$\",\n  \"^navermailapp\",\n  \"^netsurf\",\n  \"^nuclei\",\n  \"^offline explorer\",\n  \"^php\",\n  \"^postman\",\n  \"^postrank\",\n  \"^python\",\n  \"^rank\",\n  \"^read\",\n  \"^reed\",\n  \"^rest\",\n  \"^serf\",\n  \"^snapchat\",\n  \"^space bison\",\n  \"^svn\",\n  \"^swcd \",\n  \"^taringa\",\n  \"^thumbor/\",\n  \"^tumblr/\",\n  \"^user-agent:\",\n  \"^valid\",\n  \"^venus/fedoraplanet\",\n  \"^w3c\",\n  \"^webbandit/\",\n  \"^webcopier\",\n  \"^wget\",\n  \"^whatsapp\",\n  \"^xenu link sleuth\",\n  \"^yahoo\",\n  \"^yandex\",\n  \"^zdm/\\\\d\",\n  \"^zoom marketplace/\",\n  \"^{{.*}}$\",\n  \"adbeat\\\\.com\",\n  \"appinsights\",\n  \"archive\",\n  \"ask jeeves/teoma\",\n  \"bit\\\\.ly/\",\n  \"bluecoat drtr\",\n  \"browsex\",\n  \"burpcollaborator\",\n  \"capture\",\n  \"catch\",\n  \"check\",\n  \"chrome-lighthouse\",\n  \"chromeframe\",\n  \"classifier\",\n  \"cloud\",\n  \"crawl\",\n  \"cryptoapi\",\n  \"dareboost\",\n  \"datanyze\",\n  \"dataprovider\",\n  \"dejaclick\",\n  \"dmbrowser\",\n  \"download\",\n  \"evc-batch/\",\n  \"feed\",\n  \"firephp\",\n  \"freesafeip\",\n  \"gomezagent\",\n  \"headless\",\n  \"httrack\",\n  \"hubspot marketing grader\",\n  \"hydra\",\n  \"ibisbrowser\",\n  \"images\",\n  \"inspect\",\n  \"iplabel\",\n  \"ips-agent\",\n  \"java(?!;)\",\n  \"library\",\n  \"mail\\\\.ru/\",\n  \"manager\",\n  \"monitor\",\n  \"neustar wpm\",\n  \"nutch\",\n  \"offbyone\",\n  \"optimize\",\n  \"pageburst\",\n  \"parser\",\n  \"perl\",\n  \"phantom\",\n  \"pingdom\",\n  \"powermarks\",\n  \"preview\",\n  \"proxy\",\n  \"ptst[ /]\\\\d\",\n  \"reader\",\n  \"reputation\",\n  \"resolver\",\n  \"retriever\",\n  \"rexx;\",\n  \"rigor\",\n  \"robot\",\n  \"rss\",\n  \"scan\",\n  \"scrape\",\n  \"server\",\n  \"sogou\",\n  \"sparkler/\",\n  \"speedcurve\",\n  \"spider\",\n  \"splash\",\n  \"statuscake\",\n  \"stumbleupon\\\\.com\",\n  \"supercleaner\",\n  \"synapse\",\n  \"synthetic\",\n  \"torrent\",\n  \"trace\",\n  \"transcoder\",\n  \"twingly recon\",\n  \"url\",\n  \"virtuoso\",\n  \"wappalyzer\",\n  \"webglance\",\n  \"webkit2png\",\n  \"whatcms/\",\n  \"wordpress\",\n  \"zgrab\"\n];\n\n// src/index.ts\nvar naivePattern = /bot|spider|crawl|http|lighthouse/i;\nvar pattern = regularExpression;\nvar list = patterns_default;\nvar isbotNaive = (userAgent) => Boolean(userAgent) && naivePattern.test(userAgent);\nvar usedPattern;\nfunction isbot(userAgent) {\n  if (typeof usedPattern === \"undefined\") {\n    try {\n      usedPattern = new RegExp(fullPattern, \"i\");\n    } catch (error) {\n      usedPattern = naivePattern;\n    }\n  }\n  return Boolean(userAgent) && usedPattern.test(userAgent);\n}\nvar createIsbot = (customPattern) => (userAgent) => Boolean(userAgent) && customPattern.test(userAgent);\nvar createIsbotFromList = (list2) => {\n  const pattern2 = new RegExp(list2.join(\"|\"), \"i\");\n  return (userAgent) => Boolean(userAgent) && pattern2.test(userAgent);\n};\nvar isbotMatch = (userAgent) => userAgent?.match(pattern)?.[0] ?? null;\nvar isbotMatches = (userAgent) => list.map((part) => userAgent?.match(new RegExp(part, \"i\"))?.[0]).filter(Boolean);\nvar isbotPattern = (userAgent) => userAgent ? list.find((pattern2) => new RegExp(pattern2, \"i\").test(userAgent)) ?? null : null;\nvar isbotPatterns = (userAgent) => userAgent ? list.filter((pattern2) => new RegExp(pattern2, \"i\").test(userAgent)) : [];\nexport {\n  createIsbot,\n  createIsbotFromList,\n  isbot,\n  isbotMatch,\n  isbotMatches,\n  isbotNaive,\n  isbotPattern,\n  isbotPatterns,\n  list,\n  pattern\n};\n"], "mappings": ";;;AACA,IAAI,cAAc;AAClB,IAAI,oBAAoB,WAAC,m7DAAg3D,GAAC;AAG14D,IAAI,mBAAmB;AAAA,EACrB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AAGA,IAAI,eAAe;AACnB,IAAI,UAAU;AACd,IAAI,OAAO;AACX,IAAI,aAAa,CAAC,cAAc,QAAQ,SAAS,KAAK,aAAa,KAAK,SAAS;AACjF,IAAI;AACJ,SAAS,MAAM,WAAW;AACxB,MAAI,OAAO,gBAAgB,aAAa;AACtC,QAAI;AACF,oBAAc,IAAI,OAAO,aAAa,GAAG;AAAA,IAC3C,SAAS,OAAO;AACd,oBAAc;AAAA,IAChB;AAAA,EACF;AACA,SAAO,QAAQ,SAAS,KAAK,YAAY,KAAK,SAAS;AACzD;AACA,IAAI,cAAc,CAAC,kBAAkB,CAAC,cAAc,QAAQ,SAAS,KAAK,cAAc,KAAK,SAAS;AACtG,IAAI,sBAAsB,CAAC,UAAU;AACnC,QAAM,WAAW,IAAI,OAAO,MAAM,KAAK,GAAG,GAAG,GAAG;AAChD,SAAO,CAAC,cAAc,QAAQ,SAAS,KAAK,SAAS,KAAK,SAAS;AACrE;AACA,IAAI,aAAa,CAAC,cAAW;AA5M7B;AA4MgC,uDAAW,MAAM,aAAjB,mBAA4B,OAAM;AAAA;AAClE,IAAI,eAAe,CAAC,cAAc,KAAK,IAAI,CAAC,SAAM;AA7MlD;AA6MqD,sDAAW,MAAM,IAAI,OAAO,MAAM,GAAG,OAArC,mBAA0C;AAAA,CAAE,EAAE,OAAO,OAAO;AACjH,IAAI,eAAe,CAAC,cAAc,YAAY,KAAK,KAAK,CAAC,aAAa,IAAI,OAAO,UAAU,GAAG,EAAE,KAAK,SAAS,CAAC,KAAK,OAAO;AAC3H,IAAI,gBAAgB,CAAC,cAAc,YAAY,KAAK,OAAO,CAAC,aAAa,IAAI,OAAO,UAAU,GAAG,EAAE,KAAK,SAAS,CAAC,IAAI,CAAC;", "names": []}