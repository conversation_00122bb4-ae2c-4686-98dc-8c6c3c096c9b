{"version": 3, "sources": ["../src/astring.js"], "names": [], "mappings": ";;;;;;;;;;;;;;AAUA,IAAQ,SAAR,GAAsB,IAAtB,CAAQ,SAAR;;AAGA,IAAI,CAAC,MAAM,CAAC,SAAP,CAAiB,MAAtB,EAA8B;AAE5B,QAAM,IAAI,KAAJ,CACJ,+FADI,CAAN;AAGD;;AAGD,IAAI,CAAC,MAAM,CAAC,SAAP,CAAiB,QAAtB,EAAgC;AAE9B,QAAM,IAAI,KAAJ,CACJ,iGADI,CAAN;AAGD;;AAED,IAAM,mBAAmB,GAAG;AAC1B,QAAM,CADoB;AAE1B,QAAM,CAFoB;AAG1B,QAAM,CAHoB;AAI1B,OAAK,CAJqB;AAK1B,OAAK,CALqB;AAM1B,OAAK,CANqB;AAO1B,QAAM,CAPoB;AAQ1B,QAAM,CARoB;AAS1B,SAAO,CATmB;AAU1B,SAAO,CAVmB;AAW1B,OAAK,CAXqB;AAY1B,OAAK,CAZqB;AAa1B,QAAM,CAboB;AAc1B,QAAM,CAdoB;AAe1B,QAAI,CAfsB;AAgB1B,gBAAY,CAhBc;AAiB1B,QAAM,EAjBoB;AAkB1B,QAAM,EAlBoB;AAmB1B,SAAO,EAnBmB;AAoB1B,OAAK,EApBqB;AAqB1B,OAAK,EArBqB;AAsB1B,OAAK,EAtBqB;AAuB1B,OAAK,EAvBqB;AAwB1B,OAAK,EAxBqB;AAyB1B,QAAM;AAzBoB,CAA5B;AA6BO,IAAM,iBAAiB,GAAG,EAA1B;;AAEA,IAAM,sBAAsB,GAAG;AAEpC,EAAA,eAAe,EAAE,EAFmB;AAGpC,EAAA,wBAAwB,EAAE,EAHU;AAIpC,EAAA,cAAc,EAAE,EAJoB;AAKpC,EAAA,UAAU,EAAE,EALwB;AAMpC,EAAA,iBAAiB,EAAE,EANiB;AAOpC,EAAA,OAAO,EAAE,EAP2B;AAQpC,EAAA,eAAe,EAAE,EARmB;AASpC,EAAA,KAAK,EAAE,EAT6B;AAUpC,EAAA,kBAAkB,EAAE,EAVgB;AAYpC,EAAA,gBAAgB,EAAE,EAZkB;AAapC,EAAA,eAAe,EAAE,EAbmB;AAcpC,EAAA,cAAc,EAAE,EAdoB;AAepC,EAAA,aAAa,EAAE,EAfqB;AAiBpC,EAAA,uBAAuB,EAAE,iBAjBW;AAkBpC,EAAA,eAAe,EAAE,iBAlBmB;AAmBpC,EAAA,kBAAkB,EAAE,iBAnBgB;AAoBpC,EAAA,gBAAgB,EAAE,iBApBkB;AAsBpC,EAAA,gBAAgB,EAAE,EAtBkB;AAuBpC,EAAA,eAAe,EAAE,EAvBmB;AAwBpC,EAAA,eAAe,EAAE,EAxBmB;AAyBpC,EAAA,gBAAgB,EAAE,EAzBkB;AA0BpC,EAAA,iBAAiB,EAAE,EA1BiB;AA2BpC,EAAA,qBAAqB,EAAE,CA3Ba;AA4BpC,EAAA,oBAAoB,EAAE,CA5Bc;AA6BpC,EAAA,eAAe,EAAE,CA7BmB;AA8BpC,EAAA,WAAW,EAAE;AA9BuB,CAA/B;;;AAiCP,SAAS,cAAT,CAAwB,KAAxB,EAA+B,KAA/B,EAAsC;AAIpC,MAAQ,SAAR,GAAsB,KAAtB,CAAQ,SAAR;AACA,EAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;;AACA,MAAI,KAAK,IAAI,IAAT,IAAiB,KAAK,CAAC,MAAN,GAAe,CAApC,EAAuC;AACrC,IAAA,SAAS,CAAC,KAAK,CAAC,CAAD,CAAL,CAAS,IAAV,CAAT,CAAyB,KAAK,CAAC,CAAD,CAA9B,EAAmC,KAAnC;AACA,QAAQ,MAAR,GAAmB,KAAnB,CAAQ,MAAR;;AACA,SAAK,IAAI,CAAC,GAAG,CAAb,EAAgB,CAAC,GAAG,MAApB,EAA4B,CAAC,EAA7B,EAAiC;AAC/B,UAAM,KAAK,GAAG,KAAK,CAAC,CAAD,CAAnB;AACA,MAAA,KAAK,CAAC,KAAN,CAAY,IAAZ;AACA,MAAA,SAAS,CAAC,KAAK,CAAC,IAAP,CAAT,CAAsB,KAAtB,EAA6B,KAA7B;AACD;AACF;;AACD,EAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD;;AAED,SAAS,0BAAT,CAAoC,KAApC,EAA2C,IAA3C,EAAiD,UAAjD,EAA6D,WAA7D,EAA0E;AACxE,MAAM,cAAc,GAAG,KAAK,CAAC,qBAAN,CAA4B,IAAI,CAAC,IAAjC,CAAvB;;AACA,MAAI,cAAc,KAAK,iBAAvB,EAA0C;AACxC,WAAO,IAAP;AACD;;AACD,MAAM,oBAAoB,GAAG,KAAK,CAAC,qBAAN,CAA4B,UAAU,CAAC,IAAvC,CAA7B;;AACA,MAAI,cAAc,KAAK,oBAAvB,EAA6C;AAE3C,WACG,CAAC,WAAD,IACC,cAAc,KAAK,EADpB,IAEC,oBAAoB,KAAK,EAF1B,IAGC,UAAU,CAAC,QAAX,KAAwB,IAH1B,IAIA,cAAc,GAAG,oBALnB;AAOD;;AACD,MAAI,cAAc,KAAK,EAAnB,IAAyB,cAAc,KAAK,EAAhD,EAAoD;AAElD,WAAO,KAAP;AACD;;AACD,MAAI,IAAI,CAAC,QAAL,KAAkB,IAAlB,IAA0B,UAAU,CAAC,QAAX,KAAwB,IAAtD,EAA4D;AAE1D,WAAO,CAAC,WAAR;AACD;;AACD,MACE,cAAc,KAAK,EAAnB,IACA,oBAAoB,KAAK,EADzB,KAEC,IAAI,CAAC,QAAL,KAAkB,IAAlB,IAA0B,UAAU,CAAC,QAAX,KAAwB,IAFnD,CADF,EAIE;AAEA,WAAO,IAAP;AACD;;AACD,MAAI,WAAJ,EAAiB;AAEf,WACE,mBAAmB,CAAC,IAAI,CAAC,QAAN,CAAnB,IACA,mBAAmB,CAAC,UAAU,CAAC,QAAZ,CAFrB;AAID;;AACD,SACE,mBAAmB,CAAC,IAAI,CAAC,QAAN,CAAnB,GACA,mBAAmB,CAAC,UAAU,CAAC,QAAZ,CAFrB;AAID;;AAED,SAAS,gBAAT,CAA0B,KAA1B,EAAiC,IAAjC,EAAuC,UAAvC,EAAmD,WAAnD,EAAgE;AAI9D,MAAQ,SAAR,GAAsB,KAAtB,CAAQ,SAAR;;AACA,MAAI,0BAA0B,CAAC,KAAD,EAAQ,IAAR,EAAc,UAAd,EAA0B,WAA1B,CAA9B,EAAsE;AACpE,IAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACA,IAAA,SAAS,CAAC,IAAI,CAAC,IAAN,CAAT,CAAqB,IAArB,EAA2B,KAA3B;AACA,IAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD,GAJD,MAIO;AACL,IAAA,SAAS,CAAC,IAAI,CAAC,IAAN,CAAT,CAAqB,IAArB,EAA2B,KAA3B;AACD;AACF;;AAED,SAAS,QAAT,CAAkB,KAAlB,EAAyB,IAAzB,EAA+B,MAA/B,EAAuC,OAAvC,EAAgD;AAI9C,MAAM,KAAK,GAAG,IAAI,CAAC,KAAL,CAAW,IAAX,CAAd;AACA,MAAM,GAAG,GAAG,KAAK,CAAC,MAAN,GAAe,CAA3B;AACA,EAAA,KAAK,CAAC,KAAN,CAAY,KAAK,CAAC,CAAD,CAAL,CAAS,IAAT,EAAZ;;AACA,MAAI,GAAG,GAAG,CAAV,EAAa;AACX,IAAA,KAAK,CAAC,KAAN,CAAY,OAAZ;;AACA,SAAK,IAAI,CAAC,GAAG,CAAb,EAAgB,CAAC,GAAG,GAApB,EAAyB,CAAC,EAA1B,EAA8B;AAC5B,MAAA,KAAK,CAAC,KAAN,CAAY,MAAM,GAAG,KAAK,CAAC,CAAD,CAAL,CAAS,IAAT,EAAT,GAA2B,OAAvC;AACD;;AACD,IAAA,KAAK,CAAC,KAAN,CAAY,MAAM,GAAG,KAAK,CAAC,GAAD,CAAL,CAAW,IAAX,EAArB;AACD;AACF;;AAED,SAAS,cAAT,CAAwB,KAAxB,EAA+B,QAA/B,EAAyC,MAAzC,EAAiD,OAAjD,EAA0D;AAMxD,MAAQ,MAAR,GAAmB,QAAnB,CAAQ,MAAR;;AACA,OAAK,IAAI,CAAC,GAAG,CAAb,EAAgB,CAAC,GAAG,MAApB,EAA4B,CAAC,EAA7B,EAAiC;AAC/B,QAAM,OAAO,GAAG,QAAQ,CAAC,CAAD,CAAxB;AACA,IAAA,KAAK,CAAC,KAAN,CAAY,MAAZ;;AACA,QAAI,OAAO,CAAC,IAAR,CAAa,CAAb,MAAoB,GAAxB,EAA6B;AAE3B,MAAA,KAAK,CAAC,KAAN,CAAY,QAAQ,OAAO,CAAC,KAAR,CAAc,IAAd,EAAR,GAA+B,IAA3C,EAAiD,OAAjD;AACD,KAHD,MAGO;AAEL,MAAA,KAAK,CAAC,KAAN,CAAY,IAAZ;AACA,MAAA,QAAQ,CAAC,KAAD,EAAQ,OAAO,CAAC,KAAhB,EAAuB,MAAvB,EAA+B,OAA/B,CAAR;AACA,MAAA,KAAK,CAAC,KAAN,CAAY,OAAO,OAAnB;AACD;AACF;AACF;;AAED,SAAS,iBAAT,CAA2B,IAA3B,EAAiC;AAI/B,MAAI,WAAW,GAAG,IAAlB;;AACA,SAAO,WAAW,IAAI,IAAtB,EAA4B;AAC1B,uBAAiB,WAAjB;AAAA,QAAQ,IAAR,gBAAQ,IAAR;;AACA,QAAI,IAAI,CAAC,CAAD,CAAJ,KAAY,GAAZ,IAAmB,IAAI,CAAC,CAAD,CAAJ,KAAY,GAAnC,EAAwC;AAEtC,aAAO,IAAP;AACD,KAHD,MAGO,IAAI,IAAI,CAAC,CAAD,CAAJ,KAAY,GAAZ,IAAmB,IAAI,CAAC,CAAD,CAAJ,KAAY,GAA/B,IAAsC,IAAI,CAAC,CAAD,CAAJ,KAAY,GAAtD,EAA2D;AAEhE,MAAA,WAAW,GAAG,WAAW,CAAC,MAA1B;AACD,KAHM,MAGA;AACL,aAAO,KAAP;AACD;AACF;AACF;;AAED,SAAS,yBAAT,CAAmC,KAAnC,EAA0C,IAA1C,EAAgD;AAI9C,MAAQ,SAAR,GAAsB,KAAtB,CAAQ,SAAR;AACA,MAAQ,YAAR,GAAyB,IAAzB,CAAQ,YAAR;AACA,EAAA,KAAK,CAAC,KAAN,CAAY,IAAI,CAAC,IAAL,GAAY,GAAxB;AACA,MAAQ,MAAR,GAAmB,YAAnB,CAAQ,MAAR;;AACA,MAAI,MAAM,GAAG,CAAb,EAAgB;AACd,IAAA,SAAS,CAAC,kBAAV,CAA6B,YAAY,CAAC,CAAD,CAAzC,EAA8C,KAA9C;;AACA,SAAK,IAAI,CAAC,GAAG,CAAb,EAAgB,CAAC,GAAG,MAApB,EAA4B,CAAC,EAA7B,EAAiC;AAC/B,MAAA,KAAK,CAAC,KAAN,CAAY,IAAZ;AACA,MAAA,SAAS,CAAC,kBAAV,CAA6B,YAAY,CAAC,CAAD,CAAzC,EAA8C,KAA9C;AACD;AACF;AACF;;AAED,IAAI,cAAJ,EACE,mBADF,EAEE,WAFF,EAGE,gBAHF,EAIE,eAJF,EAKE,cALF;AAOO,IAAM,SAAS,GAAG;AAIvB,EAAA,OAJuB,mBAIf,IAJe,EAIT,KAJS,EAIF;AACnB,QAAM,MAAM,GAAG,KAAK,CAAC,MAAN,CAAa,MAAb,CAAoB,KAAK,CAAC,WAA1B,CAAf;AACA,QAAQ,OAAR,GAAmC,KAAnC,CAAQ,OAAR;AAAA,QAAiB,aAAjB,GAAmC,KAAnC,CAAiB,aAAjB;;AACA,QAAI,aAAa,IAAI,IAAI,CAAC,QAAL,IAAiB,IAAtC,EAA4C;AAC1C,MAAA,cAAc,CAAC,KAAD,EAAQ,IAAI,CAAC,QAAb,EAAuB,MAAvB,EAA+B,OAA/B,CAAd;AACD;;AACD,QAAM,UAAU,GAAG,IAAI,CAAC,IAAxB;AACA,QAAQ,MAAR,GAAmB,UAAnB,CAAQ,MAAR;;AACA,SAAK,IAAI,CAAC,GAAG,CAAb,EAAgB,CAAC,GAAG,MAApB,EAA4B,CAAC,EAA7B,EAAiC;AAC/B,UAAM,SAAS,GAAG,UAAU,CAAC,CAAD,CAA5B;;AACA,UAAI,aAAa,IAAI,SAAS,CAAC,QAAV,IAAsB,IAA3C,EAAiD;AAC/C,QAAA,cAAc,CAAC,KAAD,EAAQ,SAAS,CAAC,QAAlB,EAA4B,MAA5B,EAAoC,OAApC,CAAd;AACD;;AACD,MAAA,KAAK,CAAC,KAAN,CAAY,MAAZ;AACA,WAAK,SAAS,CAAC,IAAf,EAAqB,SAArB,EAAgC,KAAhC;AACA,MAAA,KAAK,CAAC,KAAN,CAAY,OAAZ;AACD;;AACD,QAAI,aAAa,IAAI,IAAI,CAAC,gBAAL,IAAyB,IAA9C,EAAoD;AAClD,MAAA,cAAc,CAAC,KAAD,EAAQ,IAAI,CAAC,gBAAb,EAA+B,MAA/B,EAAuC,OAAvC,CAAd;AACD;AACF,GAxBsB;AAyBvB,EAAA,cAAc,EAAG,cAAc,GAAG,wBAAU,IAAV,EAAgB,KAAhB,EAAuB;AACvD,QAAM,MAAM,GAAG,KAAK,CAAC,MAAN,CAAa,MAAb,CAAoB,KAAK,CAAC,WAAN,EAApB,CAAf;AACA,QAAQ,OAAR,GAAmC,KAAnC,CAAQ,OAAR;AAAA,QAAiB,aAAjB,GAAmC,KAAnC,CAAiB,aAAjB;AACA,QAAM,eAAe,GAAG,MAAM,GAAG,KAAK,CAAC,MAAvC;AACA,IAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACA,QAAM,UAAU,GAAG,IAAI,CAAC,IAAxB;;AACA,QAAI,UAAU,IAAI,IAAd,IAAsB,UAAU,CAAC,MAAX,GAAoB,CAA9C,EAAiD;AAC/C,MAAA,KAAK,CAAC,KAAN,CAAY,OAAZ;;AACA,UAAI,aAAa,IAAI,IAAI,CAAC,QAAL,IAAiB,IAAtC,EAA4C;AAC1C,QAAA,cAAc,CAAC,KAAD,EAAQ,IAAI,CAAC,QAAb,EAAuB,eAAvB,EAAwC,OAAxC,CAAd;AACD;;AACD,UAAQ,MAAR,GAAmB,UAAnB,CAAQ,MAAR;;AACA,WAAK,IAAI,CAAC,GAAG,CAAb,EAAgB,CAAC,GAAG,MAApB,EAA4B,CAAC,EAA7B,EAAiC;AAC/B,YAAM,SAAS,GAAG,UAAU,CAAC,CAAD,CAA5B;;AACA,YAAI,aAAa,IAAI,SAAS,CAAC,QAAV,IAAsB,IAA3C,EAAiD;AAC/C,UAAA,cAAc,CAAC,KAAD,EAAQ,SAAS,CAAC,QAAlB,EAA4B,eAA5B,EAA6C,OAA7C,CAAd;AACD;;AACD,QAAA,KAAK,CAAC,KAAN,CAAY,eAAZ;AACA,aAAK,SAAS,CAAC,IAAf,EAAqB,SAArB,EAAgC,KAAhC;AACA,QAAA,KAAK,CAAC,KAAN,CAAY,OAAZ;AACD;;AACD,MAAA,KAAK,CAAC,KAAN,CAAY,MAAZ;AACD,KAhBD,MAgBO;AACL,UAAI,aAAa,IAAI,IAAI,CAAC,QAAL,IAAiB,IAAtC,EAA4C;AAC1C,QAAA,KAAK,CAAC,KAAN,CAAY,OAAZ;AACA,QAAA,cAAc,CAAC,KAAD,EAAQ,IAAI,CAAC,QAAb,EAAuB,eAAvB,EAAwC,OAAxC,CAAd;AACA,QAAA,KAAK,CAAC,KAAN,CAAY,MAAZ;AACD;AACF;;AACD,QAAI,aAAa,IAAI,IAAI,CAAC,gBAAL,IAAyB,IAA9C,EAAoD;AAClD,MAAA,cAAc,CAAC,KAAD,EAAQ,IAAI,CAAC,gBAAb,EAA+B,eAA/B,EAAgD,OAAhD,CAAd;AACD;;AACD,IAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACA,IAAA,KAAK,CAAC,WAAN;AACD,GA3DsB;AA4DvB,EAAA,SAAS,EAAE,cA5DY;AA6DvB,EAAA,WA7DuB,uBA6DX,IA7DW,EA6DL,KA7DK,EA6DE;AACvB,IAAA,KAAK,CAAC,KAAN,CAAY,SAAZ;AACA,SAAK,cAAL,CAAoB,IAApB,EAA0B,KAA1B;AACD,GAhEsB;AAiEvB,EAAA,cAjEuB,0BAiER,IAjEQ,EAiEF,KAjEE,EAiEK;AAC1B,IAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD,GAnEsB;AAoEvB,EAAA,mBApEuB,+BAoEH,IApEG,EAoEG,KApEH,EAoEU;AAC/B,QAAM,UAAU,GAAG,KAAK,CAAC,qBAAN,CAA4B,IAAI,CAAC,UAAL,CAAgB,IAA5C,CAAnB;;AACA,QACE,UAAU,KAAK,iBAAf,IACC,UAAU,KAAK,CAAf,IAAoB,IAAI,CAAC,UAAL,CAAgB,IAAhB,CAAqB,IAArB,CAA0B,CAA1B,MAAiC,GAFxD,EAGE;AAEA,MAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACA,WAAK,IAAI,CAAC,UAAL,CAAgB,IAArB,EAA2B,IAAI,CAAC,UAAhC,EAA4C,KAA5C;AACA,MAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD,KARD,MAQO;AACL,WAAK,IAAI,CAAC,UAAL,CAAgB,IAArB,EAA2B,IAAI,CAAC,UAAhC,EAA4C,KAA5C;AACD;;AACD,IAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD,GAlFsB;AAmFvB,EAAA,WAnFuB,uBAmFX,IAnFW,EAmFL,KAnFK,EAmFE;AACvB,IAAA,KAAK,CAAC,KAAN,CAAY,MAAZ;AACA,SAAK,IAAI,CAAC,IAAL,CAAU,IAAf,EAAqB,IAAI,CAAC,IAA1B,EAAgC,KAAhC;AACA,IAAA,KAAK,CAAC,KAAN,CAAY,IAAZ;AACA,SAAK,IAAI,CAAC,UAAL,CAAgB,IAArB,EAA2B,IAAI,CAAC,UAAhC,EAA4C,KAA5C;;AACA,QAAI,IAAI,CAAC,SAAL,IAAkB,IAAtB,EAA4B;AAC1B,MAAA,KAAK,CAAC,KAAN,CAAY,QAAZ;AACA,WAAK,IAAI,CAAC,SAAL,CAAe,IAApB,EAA0B,IAAI,CAAC,SAA/B,EAA0C,KAA1C;AACD;AACF,GA5FsB;AA6FvB,EAAA,gBA7FuB,4BA6FN,IA7FM,EA6FA,KA7FA,EA6FO;AAC5B,SAAK,IAAI,CAAC,KAAL,CAAW,IAAhB,EAAsB,IAAI,CAAC,KAA3B,EAAkC,KAAlC;AACA,IAAA,KAAK,CAAC,KAAN,CAAY,IAAZ;AACA,SAAK,IAAI,CAAC,IAAL,CAAU,IAAf,EAAqB,IAAI,CAAC,IAA1B,EAAgC,KAAhC;AACD,GAjGsB;AAkGvB,EAAA,cAlGuB,0BAkGR,IAlGQ,EAkGF,KAlGE,EAkGK;AAC1B,IAAA,KAAK,CAAC,KAAN,CAAY,OAAZ;;AACA,QAAI,IAAI,CAAC,KAAL,IAAc,IAAlB,EAAwB;AACtB,MAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACA,WAAK,IAAI,CAAC,KAAL,CAAW,IAAhB,EAAsB,IAAI,CAAC,KAA3B,EAAkC,KAAlC;AACD;;AACD,IAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD,GAzGsB;AA0GvB,EAAA,iBA1GuB,6BA0GL,IA1GK,EA0GC,KA1GD,EA0GQ;AAC7B,IAAA,KAAK,CAAC,KAAN,CAAY,UAAZ;;AACA,QAAI,IAAI,CAAC,KAAL,IAAc,IAAlB,EAAwB;AACtB,MAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACA,WAAK,IAAI,CAAC,KAAL,CAAW,IAAhB,EAAsB,IAAI,CAAC,KAA3B,EAAkC,KAAlC;AACD;;AACD,IAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD,GAjHsB;AAkHvB,EAAA,aAlHuB,yBAkHT,IAlHS,EAkHH,KAlHG,EAkHI;AACzB,IAAA,KAAK,CAAC,KAAN,CAAY,QAAZ;AACA,SAAK,IAAI,CAAC,MAAL,CAAY,IAAjB,EAAuB,IAAI,CAAC,MAA5B,EAAoC,KAApC;AACA,IAAA,KAAK,CAAC,KAAN,CAAY,IAAZ;AACA,SAAK,IAAI,CAAC,IAAL,CAAU,IAAf,EAAqB,IAAI,CAAC,IAA1B,EAAgC,KAAhC;AACD,GAvHsB;AAwHvB,EAAA,eAxHuB,2BAwHP,IAxHO,EAwHD,KAxHC,EAwHM;AAC3B,QAAM,MAAM,GAAG,KAAK,CAAC,MAAN,CAAa,MAAb,CAAoB,KAAK,CAAC,WAAN,EAApB,CAAf;AACA,QAAQ,OAAR,GAAmC,KAAnC,CAAQ,OAAR;AAAA,QAAiB,aAAjB,GAAmC,KAAnC,CAAiB,aAAjB;AACA,IAAA,KAAK,CAAC,WAAN;AACA,QAAM,UAAU,GAAG,MAAM,GAAG,KAAK,CAAC,MAAlC;AACA,QAAM,eAAe,GAAG,UAAU,GAAG,KAAK,CAAC,MAA3C;AACA,IAAA,KAAK,CAAC,KAAN,CAAY,UAAZ;AACA,SAAK,IAAI,CAAC,YAAL,CAAkB,IAAvB,EAA6B,IAAI,CAAC,YAAlC,EAAgD,KAAhD;AACA,IAAA,KAAK,CAAC,KAAN,CAAY,QAAQ,OAApB;AACA,QAAe,UAAf,GAA8B,IAA9B,CAAQ,KAAR;AACA,QAAgB,eAAhB,GAAoC,UAApC,CAAQ,MAAR;;AACA,SAAK,IAAI,CAAC,GAAG,CAAb,EAAgB,CAAC,GAAG,eAApB,EAAqC,CAAC,EAAtC,EAA0C;AACxC,UAAM,SAAS,GAAG,UAAU,CAAC,CAAD,CAA5B;;AACA,UAAI,aAAa,IAAI,SAAS,CAAC,QAAV,IAAsB,IAA3C,EAAiD;AAC/C,QAAA,cAAc,CAAC,KAAD,EAAQ,SAAS,CAAC,QAAlB,EAA4B,UAA5B,EAAwC,OAAxC,CAAd;AACD;;AACD,UAAI,SAAS,CAAC,IAAd,EAAoB;AAClB,QAAA,KAAK,CAAC,KAAN,CAAY,UAAU,GAAG,OAAzB;AACA,aAAK,SAAS,CAAC,IAAV,CAAe,IAApB,EAA0B,SAAS,CAAC,IAApC,EAA0C,KAA1C;AACA,QAAA,KAAK,CAAC,KAAN,CAAY,MAAM,OAAlB;AACD,OAJD,MAIO;AACL,QAAA,KAAK,CAAC,KAAN,CAAY,UAAU,GAAG,UAAb,GAA0B,OAAtC;AACD;;AACD,UAAQ,UAAR,GAAuB,SAAvB,CAAQ,UAAR;AACA,UAAgB,eAAhB,GAAoC,UAApC,CAAQ,MAAR;;AACA,WAAK,IAAI,EAAC,GAAG,CAAb,EAAgB,EAAC,GAAG,eAApB,EAAqC,EAAC,EAAtC,EAA0C;AACxC,YAAM,SAAS,GAAG,UAAU,CAAC,EAAD,CAA5B;;AACA,YAAI,aAAa,IAAI,SAAS,CAAC,QAAV,IAAsB,IAA3C,EAAiD;AAC/C,UAAA,cAAc,CAAC,KAAD,EAAQ,SAAS,CAAC,QAAlB,EAA4B,eAA5B,EAA6C,OAA7C,CAAd;AACD;;AACD,QAAA,KAAK,CAAC,KAAN,CAAY,eAAZ;AACA,aAAK,SAAS,CAAC,IAAf,EAAqB,SAArB,EAAgC,KAAhC;AACA,QAAA,KAAK,CAAC,KAAN,CAAY,OAAZ;AACD;AACF;;AACD,IAAA,KAAK,CAAC,WAAN,IAAqB,CAArB;AACA,IAAA,KAAK,CAAC,KAAN,CAAY,MAAM,GAAG,GAArB;AACD,GA7JsB;AA8JvB,EAAA,eA9JuB,2BA8JP,IA9JO,EA8JD,KA9JC,EA8JM;AAC3B,IAAA,KAAK,CAAC,KAAN,CAAY,QAAZ;;AACA,QAAI,IAAI,CAAC,QAAT,EAAmB;AACjB,MAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACA,WAAK,IAAI,CAAC,QAAL,CAAc,IAAnB,EAAyB,IAAI,CAAC,QAA9B,EAAwC,KAAxC;AACD;;AACD,IAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD,GArKsB;AAsKvB,EAAA,cAtKuB,0BAsKR,IAtKQ,EAsKF,KAtKE,EAsKK;AAC1B,IAAA,KAAK,CAAC,KAAN,CAAY,QAAZ;AACA,SAAK,IAAI,CAAC,QAAL,CAAc,IAAnB,EAAyB,IAAI,CAAC,QAA9B,EAAwC,KAAxC;AACA,IAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD,GA1KsB;AA2KvB,EAAA,YA3KuB,wBA2KV,IA3KU,EA2KJ,KA3KI,EA2KG;AACxB,IAAA,KAAK,CAAC,KAAN,CAAY,MAAZ;AACA,SAAK,IAAI,CAAC,KAAL,CAAW,IAAhB,EAAsB,IAAI,CAAC,KAA3B,EAAkC,KAAlC;;AACA,QAAI,IAAI,CAAC,OAAT,EAAkB;AAChB,UAAQ,OAAR,GAAoB,IAApB,CAAQ,OAAR;;AACA,UAAI,OAAO,CAAC,KAAR,IAAiB,IAArB,EAA2B;AACzB,QAAA,KAAK,CAAC,KAAN,CAAY,SAAZ;AACD,OAFD,MAEO;AACL,QAAA,KAAK,CAAC,KAAN,CAAY,UAAZ;AACA,aAAK,OAAO,CAAC,KAAR,CAAc,IAAnB,EAAyB,OAAO,CAAC,KAAjC,EAAwC,KAAxC;AACA,QAAA,KAAK,CAAC,KAAN,CAAY,IAAZ;AACD;;AACD,WAAK,OAAO,CAAC,IAAR,CAAa,IAAlB,EAAwB,OAAO,CAAC,IAAhC,EAAsC,KAAtC;AACD;;AACD,QAAI,IAAI,CAAC,SAAT,EAAoB;AAClB,MAAA,KAAK,CAAC,KAAN,CAAY,WAAZ;AACA,WAAK,IAAI,CAAC,SAAL,CAAe,IAApB,EAA0B,IAAI,CAAC,SAA/B,EAA0C,KAA1C;AACD;AACF,GA7LsB;AA8LvB,EAAA,cA9LuB,0BA8LR,IA9LQ,EA8LF,KA9LE,EA8LK;AAC1B,IAAA,KAAK,CAAC,KAAN,CAAY,SAAZ;AACA,SAAK,IAAI,CAAC,IAAL,CAAU,IAAf,EAAqB,IAAI,CAAC,IAA1B,EAAgC,KAAhC;AACA,IAAA,KAAK,CAAC,KAAN,CAAY,IAAZ;AACA,SAAK,IAAI,CAAC,IAAL,CAAU,IAAf,EAAqB,IAAI,CAAC,IAA1B,EAAgC,KAAhC;AACD,GAnMsB;AAoMvB,EAAA,gBApMuB,4BAoMN,IApMM,EAoMA,KApMA,EAoMO;AAC5B,IAAA,KAAK,CAAC,KAAN,CAAY,KAAZ;AACA,SAAK,IAAI,CAAC,IAAL,CAAU,IAAf,EAAqB,IAAI,CAAC,IAA1B,EAAgC,KAAhC;AACA,IAAA,KAAK,CAAC,KAAN,CAAY,UAAZ;AACA,SAAK,IAAI,CAAC,IAAL,CAAU,IAAf,EAAqB,IAAI,CAAC,IAA1B,EAAgC,KAAhC;AACA,IAAA,KAAK,CAAC,KAAN,CAAY,IAAZ;AACD,GA1MsB;AA2MvB,EAAA,YA3MuB,wBA2MV,IA3MU,EA2MJ,KA3MI,EA2MG;AACxB,IAAA,KAAK,CAAC,KAAN,CAAY,OAAZ;;AACA,QAAI,IAAI,CAAC,IAAL,IAAa,IAAjB,EAAuB;AACrB,UAAQ,IAAR,GAAiB,IAAjB,CAAQ,IAAR;;AACA,UAAI,IAAI,CAAC,IAAL,CAAU,CAAV,MAAiB,GAArB,EAA0B;AACxB,QAAA,yBAAyB,CAAC,KAAD,EAAQ,IAAR,CAAzB;AACD,OAFD,MAEO;AACL,aAAK,IAAI,CAAC,IAAV,EAAgB,IAAhB,EAAsB,KAAtB;AACD;AACF;;AACD,IAAA,KAAK,CAAC,KAAN,CAAY,IAAZ;;AACA,QAAI,IAAI,CAAC,IAAT,EAAe;AACb,WAAK,IAAI,CAAC,IAAL,CAAU,IAAf,EAAqB,IAAI,CAAC,IAA1B,EAAgC,KAAhC;AACD;;AACD,IAAA,KAAK,CAAC,KAAN,CAAY,IAAZ;;AACA,QAAI,IAAI,CAAC,MAAT,EAAiB;AACf,WAAK,IAAI,CAAC,MAAL,CAAY,IAAjB,EAAuB,IAAI,CAAC,MAA5B,EAAoC,KAApC;AACD;;AACD,IAAA,KAAK,CAAC,KAAN,CAAY,IAAZ;AACA,SAAK,IAAI,CAAC,IAAL,CAAU,IAAf,EAAqB,IAAI,CAAC,IAA1B,EAAgC,KAAhC;AACD,GA/NsB;AAgOvB,EAAA,cAAc,EAAG,cAAc,GAAG,wBAAU,IAAV,EAAgB,KAAhB,EAAuB;AACvD,IAAA,KAAK,CAAC,KAAN,eAAmB,IAAI,SAAJ,GAAa,QAAb,GAAwB,EAA3C;AACA,QAAQ,IAAR,GAAiB,IAAjB,CAAQ,IAAR;;AACA,QAAI,IAAI,CAAC,IAAL,CAAU,CAAV,MAAiB,GAArB,EAA0B;AACxB,MAAA,yBAAyB,CAAC,KAAD,EAAQ,IAAR,CAAzB;AACD,KAFD,MAEO;AACL,WAAK,IAAI,CAAC,IAAV,EAAgB,IAAhB,EAAsB,KAAtB;AACD;;AAED,IAAA,KAAK,CAAC,KAAN,CAAY,IAAI,CAAC,IAAL,CAAU,CAAV,MAAiB,GAAjB,GAAuB,MAAvB,GAAgC,MAA5C;AACA,SAAK,IAAI,CAAC,KAAL,CAAW,IAAhB,EAAsB,IAAI,CAAC,KAA3B,EAAkC,KAAlC;AACA,IAAA,KAAK,CAAC,KAAN,CAAY,IAAZ;AACA,SAAK,IAAI,CAAC,IAAL,CAAU,IAAf,EAAqB,IAAI,CAAC,IAA1B,EAAgC,KAAhC;AACD,GA7OsB;AA8OvB,EAAA,cAAc,EAAE,cA9OO;AA+OvB,EAAA,iBA/OuB,6BA+OL,IA/OK,EA+OC,KA/OD,EA+OQ;AAC7B,IAAA,KAAK,CAAC,KAAN,CAAY,WAAZ,EAAyB,IAAzB;AACD,GAjPsB;AAkPvB,EAAA,mBAAmB,EAAG,mBAAmB,GAAG,6BAAU,IAAV,EAAgB,KAAhB,EAAuB;AACjE,IAAA,KAAK,CAAC,KAAN,CACE,CAAC,IAAI,CAAC,KAAL,GAAa,QAAb,GAAwB,EAAzB,KACG,IAAI,CAAC,SAAL,GAAiB,YAAjB,GAAgC,WADnC,KAEG,IAAI,CAAC,EAAL,GAAU,IAAI,CAAC,EAAL,CAAQ,IAAlB,GAAyB,EAF5B,CADF,EAIE,IAJF;AAMA,IAAA,cAAc,CAAC,KAAD,EAAQ,IAAI,CAAC,MAAb,CAAd;AACA,IAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACA,SAAK,IAAI,CAAC,IAAL,CAAU,IAAf,EAAqB,IAAI,CAAC,IAA1B,EAAgC,KAAhC;AACD,GA5PsB;AA6PvB,EAAA,kBAAkB,EAAE,mBA7PG;AA8PvB,EAAA,mBA9PuB,+BA8PH,IA9PG,EA8PG,KA9PH,EA8PU;AAC/B,IAAA,yBAAyB,CAAC,KAAD,EAAQ,IAAR,CAAzB;AACA,IAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD,GAjQsB;AAkQvB,EAAA,kBAlQuB,8BAkQJ,IAlQI,EAkQE,KAlQF,EAkQS;AAC9B,SAAK,IAAI,CAAC,EAAL,CAAQ,IAAb,EAAmB,IAAI,CAAC,EAAxB,EAA4B,KAA5B;;AACA,QAAI,IAAI,CAAC,IAAL,IAAa,IAAjB,EAAuB;AACrB,MAAA,KAAK,CAAC,KAAN,CAAY,KAAZ;AACA,WAAK,IAAI,CAAC,IAAL,CAAU,IAAf,EAAqB,IAAI,CAAC,IAA1B,EAAgC,KAAhC;AACD;AACF,GAxQsB;AAyQvB,EAAA,gBAzQuB,4BAyQN,IAzQM,EAyQA,KAzQA,EAyQO;AAC5B,IAAA,KAAK,CAAC,KAAN,CAAY,YAAY,IAAI,CAAC,EAAL,aAAa,IAAI,CAAC,EAAL,CAAQ,IAArB,SAA+B,EAA3C,CAAZ,EAA4D,IAA5D;;AACA,QAAI,IAAI,CAAC,UAAT,EAAqB;AACnB,MAAA,KAAK,CAAC,KAAN,CAAY,UAAZ;AACA,UAAQ,UAAR,GAAuB,IAAvB,CAAQ,UAAR;AACA,UAAQ,IAAR,GAAiB,UAAjB,CAAQ,IAAR;AACA,UAAM,UAAU,GAAG,KAAK,CAAC,qBAAN,CAA4B,IAA5B,CAAnB;;AACA,UACE,CAAC,IAAI,CAAC,CAAD,CAAJ,KAAY,GAAZ,IAAmB,IAAI,CAAC,CAAD,CAAJ,KAAY,GAA/B,IAAsC,IAAI,CAAC,CAAD,CAAJ,KAAY,GAAnD,MACC,UAAU,KAAK,iBAAf,IACC,UAAU,GAAG,KAAK,CAAC,qBAAN,CAA4B,eAF3C,CADF,EAIE;AAEA,QAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACA,aAAK,IAAI,CAAC,UAAL,CAAgB,IAArB,EAA2B,UAA3B,EAAuC,KAAvC;AACA,QAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD,OATD,MASO;AACL,aAAK,UAAU,CAAC,IAAhB,EAAsB,UAAtB,EAAkC,KAAlC;AACD;;AACD,MAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD;;AACD,SAAK,SAAL,CAAe,IAAI,CAAC,IAApB,EAA0B,KAA1B;AACD,GA/RsB;AAgSvB,EAAA,iBAhSuB,6BAgSL,IAhSK,EAgSC,KAhSD,EAgSQ;AAC7B,IAAA,KAAK,CAAC,KAAN,CAAY,SAAZ;AACA,QAAQ,UAAR,GAAmC,IAAnC,CAAQ,UAAR;AAAA,QAAoB,UAApB,GAAmC,IAAnC,CAAoB,UAApB;AACA,QAAQ,MAAR,GAAmB,UAAnB,CAAQ,MAAR;AAGA,QAAI,CAAC,GAAG,CAAR;;AACA,QAAI,MAAM,GAAG,CAAb,EAAgB;AACd,aAAO,CAAC,GAAG,MAAX,GAAqB;AACnB,YAAI,CAAC,GAAG,CAAR,EAAW;AACT,UAAA,KAAK,CAAC,KAAN,CAAY,IAAZ;AACD;;AACD,YAAM,SAAS,GAAG,UAAU,CAAC,CAAD,CAA5B;AACA,YAAM,IAAI,GAAG,SAAS,CAAC,IAAV,CAAe,CAAf,CAAb;;AACA,YAAI,IAAI,KAAK,GAAb,EAAkB;AAEhB,UAAA,KAAK,CAAC,KAAN,CAAY,SAAS,CAAC,KAAV,CAAgB,IAA5B,EAAkC,SAAlC;AACA,UAAA,CAAC;AACF,SAJD,MAIO,IAAI,IAAI,KAAK,GAAb,EAAkB;AAEvB,UAAA,KAAK,CAAC,KAAN,CAAY,UAAU,SAAS,CAAC,KAAV,CAAgB,IAAtC,EAA4C,SAA5C;AACA,UAAA,CAAC;AACF,SAJM,MAIA;AAEL;AACD;AACF;;AACD,UAAI,CAAC,GAAG,MAAR,EAAgB;AACd,QAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;;AACA,iBAAS;AACP,cAAM,UAAS,GAAG,UAAU,CAAC,CAAD,CAA5B;AACA,cAAQ,IAAR,GAAiB,UAAS,CAAC,QAA3B,CAAQ,IAAR;AACA,UAAA,KAAK,CAAC,KAAN,CAAY,IAAZ,EAAkB,UAAlB;;AACA,cAAI,IAAI,KAAK,UAAS,CAAC,KAAV,CAAgB,IAA7B,EAAmC;AACjC,YAAA,KAAK,CAAC,KAAN,CAAY,SAAS,UAAS,CAAC,KAAV,CAAgB,IAArC;AACD;;AACD,cAAI,EAAE,CAAF,GAAM,MAAV,EAAkB;AAChB,YAAA,KAAK,CAAC,KAAN,CAAY,IAAZ;AACD,WAFD,MAEO;AACL;AACD;AACF;;AACD,QAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD;;AACD,MAAA,KAAK,CAAC,KAAN,CAAY,QAAZ;AACD;;AACD,SAAK,OAAL,CAAa,IAAI,CAAC,MAAlB,EAA0B,KAA1B;;AAEA,QAAI,UAAU,IAAI,UAAU,CAAC,MAAX,GAAoB,CAAtC,EAAyC;AACvC,MAAA,KAAK,CAAC,KAAN,CAAY,UAAZ;;AACA,WAAK,IAAI,GAAC,GAAG,CAAb,EAAgB,GAAC,GAAG,UAAU,CAAC,MAA/B,EAAuC,GAAC,EAAxC,EAA4C;AAC1C,aAAK,eAAL,CAAqB,UAAU,CAAC,GAAD,CAA/B,EAAoC,KAApC;AACA,YAAI,GAAC,GAAG,UAAU,CAAC,MAAX,GAAoB,CAA5B,EAA+B,KAAK,CAAC,KAAN,CAAY,IAAZ;AAChC;;AAED,MAAA,KAAK,CAAC,KAAN,CAAY,IAAZ;AACD;;AACD,IAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD,GA1VsB;AA2VvB,EAAA,eA3VuB,2BA2VP,IA3VO,EA2VD,KA3VC,EA2VM;AAC3B,SAAK,UAAL,CAAgB,IAAI,CAAC,GAArB,EAA0B,KAA1B;AACA,IAAA,KAAK,CAAC,KAAN,CAAY,IAAZ;AACA,SAAK,OAAL,CAAa,IAAI,CAAC,KAAlB,EAAyB,KAAzB;AACD,GA/VsB;AAgWvB,EAAA,gBAhWuB,4BAgWN,IAhWM,EAgWA,KAhWA,EAgWO;AAC5B,IAAA,KAAK,CAAC,KAAN,CAAY,SAAZ;AACA,SAAK,IAAI,CAAC,MAAL,CAAY,IAAjB,EAAuB,IAAI,CAAC,MAA5B,EAAoC,KAApC;AACA,IAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD,GApWsB;AAqWvB,EAAA,wBArWuB,oCAqWE,IArWF,EAqWQ,KArWR,EAqWe;AACpC,IAAA,KAAK,CAAC,KAAN,CAAY,iBAAZ;AACA,SAAK,IAAI,CAAC,WAAL,CAAiB,IAAtB,EAA4B,IAAI,CAAC,WAAjC,EAA8C,KAA9C;;AACA,QACE,KAAK,CAAC,qBAAN,CAA4B,IAAI,CAAC,WAAL,CAAiB,IAA7C,KAAsD,IAAtD,IACA,IAAI,CAAC,WAAL,CAAiB,IAAjB,CAAsB,CAAtB,MAA6B,GAF/B,EAGE;AAEA,MAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD;AACF,GA/WsB;AAgXvB,EAAA,sBAhXuB,kCAgXA,IAhXA,EAgXM,KAhXN,EAgXa;AAClC,IAAA,KAAK,CAAC,KAAN,CAAY,SAAZ;;AACA,QAAI,IAAI,CAAC,WAAT,EAAsB;AACpB,WAAK,IAAI,CAAC,WAAL,CAAiB,IAAtB,EAA4B,IAAI,CAAC,WAAjC,EAA8C,KAA9C;AACD,KAFD,MAEO;AACL,MAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACM,UAAE,UAAF,GAAiB,IAAjB,CAAE,UAAF;AAAA,UACF,MADE,GACS,UADT,CACF,MADE;;AAEN,UAAI,MAAM,GAAG,CAAb,EAAgB;AACd,aAAK,IAAI,CAAC,GAAG,CAAb,IAAoB;AAClB,cAAM,SAAS,GAAG,UAAU,CAAC,CAAD,CAA5B;AACA,cAAQ,IAAR,GAAiB,SAAS,CAAC,KAA3B,CAAQ,IAAR;AACA,UAAA,KAAK,CAAC,KAAN,CAAY,IAAZ,EAAkB,SAAlB;;AACA,cAAI,IAAI,KAAK,SAAS,CAAC,QAAV,CAAmB,IAAhC,EAAsC;AACpC,YAAA,KAAK,CAAC,KAAN,CAAY,SAAS,SAAS,CAAC,QAAV,CAAmB,IAAxC;AACD;;AACD,cAAI,EAAE,CAAF,GAAM,MAAV,EAAkB;AAChB,YAAA,KAAK,CAAC,KAAN,CAAY,IAAZ;AACD,WAFD,MAEO;AACL;AACD;AACF;AACF;;AACD,MAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;;AACA,UAAI,IAAI,CAAC,MAAT,EAAiB;AACf,QAAA,KAAK,CAAC,KAAN,CAAY,QAAZ;AACA,aAAK,OAAL,CAAa,IAAI,CAAC,MAAlB,EAA0B,KAA1B;AACD;;AAED,UAAI,IAAI,CAAC,UAAL,IAAmB,IAAI,CAAC,UAAL,CAAgB,MAAhB,GAAyB,CAAhD,EAAmD;AACjD,QAAA,KAAK,CAAC,KAAN,CAAY,UAAZ;;AACA,aAAK,IAAI,GAAC,GAAG,CAAb,EAAgB,GAAC,GAAG,IAAI,CAAC,UAAL,CAAgB,MAApC,EAA4C,GAAC,EAA7C,EAAiD;AAC/C,eAAK,eAAL,CAAqB,IAAI,CAAC,UAAL,CAAgB,GAAhB,CAArB,EAAyC,KAAzC;AACA,cAAI,GAAC,GAAG,IAAI,CAAC,UAAL,CAAgB,MAAhB,GAAyB,CAAjC,EAAoC,KAAK,CAAC,KAAN,CAAY,IAAZ;AACrC;;AAED,QAAA,KAAK,CAAC,KAAN,CAAY,IAAZ;AACD;;AAED,MAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD;AACF,GAzZsB;AA0ZvB,EAAA,oBA1ZuB,gCA0ZF,IA1ZE,EA0ZI,KA1ZJ,EA0ZW;AAChC,QAAI,IAAI,CAAC,QAAL,IAAiB,IAArB,EAA2B;AACzB,MAAA,KAAK,CAAC,KAAN,CAAY,iBAAiB,IAAI,CAAC,QAAL,CAAc,IAA/B,GAAsC,QAAlD;AACD,KAFD,MAEO;AACL,MAAA,KAAK,CAAC,KAAN,CAAY,gBAAZ;AACD;;AACD,SAAK,OAAL,CAAa,IAAI,CAAC,MAAlB,EAA0B,KAA1B;;AAEA,QAAI,IAAI,CAAC,UAAL,IAAmB,IAAI,CAAC,UAAL,CAAgB,MAAhB,GAAyB,CAAhD,EAAmD;AACjD,MAAA,KAAK,CAAC,KAAN,CAAY,UAAZ;;AACA,WAAK,IAAI,CAAC,GAAG,CAAb,EAAgB,CAAC,GAAG,IAAI,CAAC,UAAL,CAAgB,MAApC,EAA4C,CAAC,EAA7C,EAAiD;AAC/C,aAAK,eAAL,CAAqB,IAAI,CAAC,UAAL,CAAgB,CAAhB,CAArB,EAAyC,KAAzC;AACA,YAAI,CAAC,GAAG,IAAI,CAAC,UAAL,CAAgB,MAAhB,GAAyB,CAAjC,EAAoC,KAAK,CAAC,KAAN,CAAY,IAAZ;AACrC;;AAED,MAAA,KAAK,CAAC,KAAN,CAAY,IAAZ;AACD;;AAED,IAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD,GA7asB;AA8avB,EAAA,gBA9auB,4BA8aN,IA9aM,EA8aA,KA9aA,EA8aO;AAC5B,QAAI,IAAI,UAAR,EAAiB;AACf,MAAA,KAAK,CAAC,KAAN,CAAY,SAAZ;AACD;;AACD,QAAM,IAAI,GAAG,IAAI,CAAC,IAAL,CAAU,CAAV,CAAb;;AACA,QAAI,IAAI,KAAK,GAAT,IAAgB,IAAI,KAAK,GAA7B,EAAkC;AAEhC,MAAA,KAAK,CAAC,KAAN,CAAY,IAAI,CAAC,IAAL,GAAY,GAAxB;AACD;;AACD,QAAI,IAAI,CAAC,KAAL,CAAW,KAAf,EAAsB;AACpB,MAAA,KAAK,CAAC,KAAN,CAAY,QAAZ;AACD;;AACD,QAAI,IAAI,CAAC,KAAL,CAAW,SAAf,EAA0B;AACxB,MAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD;;AACD,QAAI,IAAI,CAAC,QAAT,EAAmB;AACjB,MAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACA,WAAK,IAAI,CAAC,GAAL,CAAS,IAAd,EAAoB,IAAI,CAAC,GAAzB,EAA8B,KAA9B;AACA,MAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD,KAJD,MAIO;AACL,WAAK,IAAI,CAAC,GAAL,CAAS,IAAd,EAAoB,IAAI,CAAC,GAAzB,EAA8B,KAA9B;AACD;;AACD,IAAA,cAAc,CAAC,KAAD,EAAQ,IAAI,CAAC,KAAL,CAAW,MAAnB,CAAd;AACA,IAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACA,SAAK,IAAI,CAAC,KAAL,CAAW,IAAX,CAAgB,IAArB,EAA2B,IAAI,CAAC,KAAL,CAAW,IAAtC,EAA4C,KAA5C;AACD,GAvcsB;AAwcvB,EAAA,eAxcuB,2BAwcP,IAxcO,EAwcD,KAxcC,EAwcM;AAC3B,SAAK,gBAAL,CAAsB,IAAtB,EAA4B,KAA5B;AACD,GA1csB;AA2cvB,EAAA,uBA3cuB,mCA2cC,IA3cD,EA2cO,KA3cP,EA2cc;AACnC,IAAA,KAAK,CAAC,KAAN,CAAY,IAAI,CAAC,KAAL,GAAa,QAAb,GAAwB,EAApC,EAAwC,IAAxC;AACA,QAAQ,MAAR,GAAmB,IAAnB,CAAQ,MAAR;;AACA,QAAI,MAAM,IAAI,IAAd,EAAoB;AAElB,UAAI,MAAM,CAAC,MAAP,KAAkB,CAAlB,IAAuB,MAAM,CAAC,CAAD,CAAN,CAAU,IAAV,CAAe,CAAf,MAAsB,GAAjD,EAAsD;AAEpD,QAAA,KAAK,CAAC,KAAN,CAAY,MAAM,CAAC,CAAD,CAAN,CAAU,IAAtB,EAA4B,MAAM,CAAC,CAAD,CAAlC;AACD,OAHD,MAGO;AACL,QAAA,cAAc,CAAC,KAAD,EAAQ,IAAI,CAAC,MAAb,CAAd;AACD;AACF;;AACD,IAAA,KAAK,CAAC,KAAN,CAAY,MAAZ;;AACA,QAAI,IAAI,CAAC,IAAL,CAAU,IAAV,CAAe,CAAf,MAAsB,GAA1B,EAA+B;AAE7B,MAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACA,WAAK,gBAAL,CAAsB,IAAI,CAAC,IAA3B,EAAiC,KAAjC;AACA,MAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD,KALD,MAKO;AACL,WAAK,IAAI,CAAC,IAAL,CAAU,IAAf,EAAqB,IAAI,CAAC,IAA1B,EAAgC,KAAhC;AACD;AACF,GAhesB;AAievB,EAAA,cAjeuB,0BAieR,IAjeQ,EAieF,KAjeE,EAieK;AAC1B,IAAA,KAAK,CAAC,KAAN,CAAY,MAAZ,EAAoB,IAApB;AACD,GAnesB;AAoevB,EAAA,KApeuB,iBAoejB,IApeiB,EAoeX,KApeW,EAoeJ;AACjB,IAAA,KAAK,CAAC,KAAN,CAAY,OAAZ,EAAqB,IAArB;AACD,GAtesB;AAuevB,EAAA,WAAW,EAAG,WAAW,GAAG,qBAAU,IAAV,EAAgB,KAAhB,EAAuB;AACjD,IAAA,KAAK,CAAC,KAAN,CAAY,KAAZ;AACA,SAAK,IAAI,CAAC,QAAL,CAAc,IAAnB,EAAyB,IAAI,CAAC,QAA9B,EAAwC,KAAxC;AACD,GA1esB;AA2evB,EAAA,aAAa,EAAE,WA3eQ;AA4evB,EAAA,eA5euB,2BA4eP,IA5eO,EA4eD,KA5eC,EA4eM;AAC3B,IAAA,KAAK,CAAC,KAAN,CAAY,IAAI,CAAC,QAAL,GAAgB,QAAhB,GAA2B,OAAvC;;AACA,QAAI,IAAI,CAAC,QAAT,EAAmB;AACjB,MAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACA,WAAK,IAAI,CAAC,QAAL,CAAc,IAAnB,EAAyB,IAAI,CAAC,QAA9B,EAAwC,KAAxC;AACD;AACF,GAlfsB;AAmfvB,EAAA,eAnfuB,2BAmfP,IAnfO,EAmfD,KAnfC,EAmfM;AAC3B,IAAA,KAAK,CAAC,KAAN,CAAY,QAAZ,EAAsB,IAAtB;AACA,IAAA,gBAAgB,CAAC,KAAD,EAAQ,IAAI,CAAC,QAAb,EAAuB,IAAvB,CAAhB;AACD,GAtfsB;AAufvB,EAAA,eAvfuB,2BAufP,IAvfO,EAufD,KAvfC,EAufM;AAC3B,QAAQ,MAAR,GAAgC,IAAhC,CAAQ,MAAR;AAAA,QAAgB,WAAhB,GAAgC,IAAhC,CAAgB,WAAhB;AACA,IAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACA,QAAQ,MAAR,GAAmB,WAAnB,CAAQ,MAAR;;AACA,SAAK,IAAI,CAAC,GAAG,CAAb,EAAgB,CAAC,GAAG,MAApB,EAA4B,CAAC,EAA7B,EAAiC;AAC/B,UAAM,UAAU,GAAG,WAAW,CAAC,CAAD,CAA9B;AACA,UAAM,MAAK,GAAG,MAAM,CAAC,CAAD,CAApB;AACA,MAAA,KAAK,CAAC,KAAN,CAAY,MAAK,CAAC,KAAN,CAAY,GAAxB,EAA6B,MAA7B;AACA,MAAA,KAAK,CAAC,KAAN,CAAY,IAAZ;AACA,WAAK,UAAU,CAAC,IAAhB,EAAsB,UAAtB,EAAkC,KAAlC;AACA,MAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD;;AACD,QAAM,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,MAAP,GAAgB,CAAjB,CAApB;AACA,IAAA,KAAK,CAAC,KAAN,CAAY,KAAK,CAAC,KAAN,CAAY,GAAxB,EAA6B,KAA7B;AACA,IAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD,GAtgBsB;AAugBvB,EAAA,eAvgBuB,2BAugBP,IAvgBO,EAugBD,KAvgBC,EAugBM;AAC3B,IAAA,KAAK,CAAC,KAAN,CAAY,IAAI,CAAC,KAAL,CAAW,GAAvB,EAA4B,IAA5B;AACD,GAzgBsB;AA0gBvB,EAAA,wBA1gBuB,oCA0gBE,IA1gBF,EA0gBQ,KA1gBR,EA0gBe;AACpC,IAAA,gBAAgB,CAAC,KAAD,EAAQ,IAAI,CAAC,GAAb,EAAkB,IAAlB,CAAhB;AACA,SAAK,IAAI,CAAC,KAAL,CAAW,IAAhB,EAAsB,IAAI,CAAC,KAA3B,EAAkC,KAAlC;AACD,GA7gBsB;AA8gBvB,EAAA,eAAe,EAAG,eAAe,GAAG,yBAAU,IAAV,EAAgB,KAAhB,EAAuB;AACzD,IAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;;AACA,QAAI,IAAI,CAAC,QAAL,CAAc,MAAd,GAAuB,CAA3B,EAA8B;AACtB,UAAE,QAAF,GAAe,IAAf,CAAE,QAAF;AAAA,UACF,MADE,GACS,QADT,CACF,MADE;;AAEN,WAAK,IAAI,CAAC,GAAG,CAAb,IAAoB;AAClB,YAAM,OAAO,GAAG,QAAQ,CAAC,CAAD,CAAxB;;AACA,YAAI,OAAO,IAAI,IAAf,EAAqB;AACnB,eAAK,OAAO,CAAC,IAAb,EAAmB,OAAnB,EAA4B,KAA5B;AACD;;AACD,YAAI,EAAE,CAAF,GAAM,MAAV,EAAkB;AAChB,UAAA,KAAK,CAAC,KAAN,CAAY,IAAZ;AACD,SAFD,MAEO;AACL,cAAI,OAAO,IAAI,IAAf,EAAqB;AACnB,YAAA,KAAK,CAAC,KAAN,CAAY,IAAZ;AACD;;AACD;AACD;AACF;AACF;;AACD,IAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD,GAniBsB;AAoiBvB,EAAA,YAAY,EAAE,eApiBS;AAqiBvB,EAAA,gBAriBuB,4BAqiBN,IAriBM,EAqiBA,KAriBA,EAqiBO;AAC5B,QAAM,MAAM,GAAG,KAAK,CAAC,MAAN,CAAa,MAAb,CAAoB,KAAK,CAAC,WAAN,EAApB,CAAf;AACA,QAAQ,OAAR,GAAmC,KAAnC,CAAQ,OAAR;AAAA,QAAiB,aAAjB,GAAmC,KAAnC,CAAiB,aAAjB;AACA,QAAM,cAAc,GAAG,MAAM,GAAG,KAAK,CAAC,MAAtC;AACA,IAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;;AACA,QAAI,IAAI,CAAC,UAAL,CAAgB,MAAhB,GAAyB,CAA7B,EAAgC;AAC9B,MAAA,KAAK,CAAC,KAAN,CAAY,OAAZ;;AACA,UAAI,aAAa,IAAI,IAAI,CAAC,QAAL,IAAiB,IAAtC,EAA4C;AAC1C,QAAA,cAAc,CAAC,KAAD,EAAQ,IAAI,CAAC,QAAb,EAAuB,cAAvB,EAAuC,OAAvC,CAAd;AACD;;AACD,UAAM,KAAK,GAAG,MAAM,OAApB;AACM,UAAE,UAAF,GAAiB,IAAjB,CAAE,UAAF;AAAA,UACF,MADE,GACS,UADT,CACF,MADE;;AAEN,WAAK,IAAI,CAAC,GAAG,CAAb,IAAoB;AAClB,YAAM,QAAQ,GAAG,UAAU,CAAC,CAAD,CAA3B;;AACA,YAAI,aAAa,IAAI,QAAQ,CAAC,QAAT,IAAqB,IAA1C,EAAgD;AAC9C,UAAA,cAAc,CAAC,KAAD,EAAQ,QAAQ,CAAC,QAAjB,EAA2B,cAA3B,EAA2C,OAA3C,CAAd;AACD;;AACD,QAAA,KAAK,CAAC,KAAN,CAAY,cAAZ;AACA,aAAK,QAAQ,CAAC,IAAd,EAAoB,QAApB,EAA8B,KAA9B;;AACA,YAAI,EAAE,CAAF,GAAM,MAAV,EAAkB;AAChB,UAAA,KAAK,CAAC,KAAN,CAAY,KAAZ;AACD,SAFD,MAEO;AACL;AACD;AACF;;AACD,MAAA,KAAK,CAAC,KAAN,CAAY,OAAZ;;AACA,UAAI,aAAa,IAAI,IAAI,CAAC,gBAAL,IAAyB,IAA9C,EAAoD;AAClD,QAAA,cAAc,CAAC,KAAD,EAAQ,IAAI,CAAC,gBAAb,EAA+B,cAA/B,EAA+C,OAA/C,CAAd;AACD;;AACD,MAAA,KAAK,CAAC,KAAN,CAAY,MAAM,GAAG,GAArB;AACD,KA1BD,MA0BO,IAAI,aAAJ,EAAmB;AACxB,UAAI,IAAI,CAAC,QAAL,IAAiB,IAArB,EAA2B;AACzB,QAAA,KAAK,CAAC,KAAN,CAAY,OAAZ;AACA,QAAA,cAAc,CAAC,KAAD,EAAQ,IAAI,CAAC,QAAb,EAAuB,cAAvB,EAAuC,OAAvC,CAAd;;AACA,YAAI,IAAI,CAAC,gBAAL,IAAyB,IAA7B,EAAmC;AACjC,UAAA,cAAc,CAAC,KAAD,EAAQ,IAAI,CAAC,gBAAb,EAA+B,cAA/B,EAA+C,OAA/C,CAAd;AACD;;AACD,QAAA,KAAK,CAAC,KAAN,CAAY,MAAM,GAAG,GAArB;AACD,OAPD,MAOO,IAAI,IAAI,CAAC,gBAAL,IAAyB,IAA7B,EAAmC;AACxC,QAAA,KAAK,CAAC,KAAN,CAAY,OAAZ;AACA,QAAA,cAAc,CAAC,KAAD,EAAQ,IAAI,CAAC,gBAAb,EAA+B,cAA/B,EAA+C,OAA/C,CAAd;AACA,QAAA,KAAK,CAAC,KAAN,CAAY,MAAM,GAAG,GAArB;AACD,OAJM,MAIA;AACL,QAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD;AACF,KAfM,MAeA;AACL,MAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD;;AACD,IAAA,KAAK,CAAC,WAAN;AACD,GAvlBsB;AAwlBvB,EAAA,QAxlBuB,oBAwlBd,IAxlBc,EAwlBR,KAxlBQ,EAwlBD;AACpB,QAAI,IAAI,CAAC,MAAL,IAAe,IAAI,CAAC,IAAL,CAAU,CAAV,MAAiB,GAApC,EAAyC;AAEvC,WAAK,gBAAL,CAAsB,IAAtB,EAA4B,KAA5B;AACD,KAHD,MAGO;AACL,UAAI,CAAC,IAAI,CAAC,SAAV,EAAqB;AACnB,YAAI,IAAI,CAAC,QAAT,EAAmB;AACjB,UAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACA,eAAK,IAAI,CAAC,GAAL,CAAS,IAAd,EAAoB,IAAI,CAAC,GAAzB,EAA8B,KAA9B;AACA,UAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD,SAJD,MAIO;AACL,eAAK,IAAI,CAAC,GAAL,CAAS,IAAd,EAAoB,IAAI,CAAC,GAAzB,EAA8B,KAA9B;AACD;;AACD,QAAA,KAAK,CAAC,KAAN,CAAY,IAAZ;AACD;;AACD,WAAK,IAAI,CAAC,KAAL,CAAW,IAAhB,EAAsB,IAAI,CAAC,KAA3B,EAAkC,KAAlC;AACD;AACF,GAzmBsB;AA0mBvB,EAAA,kBA1mBuB,8BA0mBJ,IA1mBI,EA0mBE,KA1mBF,EA0mBS;AAC9B,QAAI,IAAI,UAAR,EAAiB;AACf,MAAA,KAAK,CAAC,KAAN,CAAY,SAAZ;AACD;;AACD,QAAI,IAAI,CAAC,QAAT,EAAmB;AACjB,MAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD;;AACD,SAAK,IAAI,CAAC,GAAL,CAAS,IAAd,EAAoB,IAAI,CAAC,GAAzB,EAA8B,KAA9B;;AACA,QAAI,IAAI,CAAC,QAAT,EAAmB;AACjB,MAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD;;AACD,QAAI,IAAI,CAAC,KAAL,IAAc,IAAlB,EAAwB;AACtB,UAAI,IAAI,CAAC,GAAL,CAAS,IAAT,CAAc,CAAd,MAAqB,GAAzB,EAA8B;AAC5B,QAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD;;AACD;AACD;;AACD,IAAA,KAAK,CAAC,KAAN,CAAY,KAAZ;AACA,SAAK,IAAI,CAAC,KAAL,CAAW,IAAhB,EAAsB,IAAI,CAAC,KAA3B,EAAkC,KAAlC;AACA,IAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD,GA9nBsB;AA+nBvB,EAAA,aA/nBuB,yBA+nBT,IA/nBS,EA+nBH,KA/nBG,EA+nBI;AACzB,IAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;;AACA,QAAI,IAAI,CAAC,UAAL,CAAgB,MAAhB,GAAyB,CAA7B,EAAgC;AACxB,UAAE,UAAF,GAAiB,IAAjB,CAAE,UAAF;AAAA,UACF,MADE,GACS,UADT,CACF,MADE;;AAEN,WAAK,IAAI,CAAC,GAAG,CAAb,IAAoB;AAClB,aAAK,UAAU,CAAC,CAAD,CAAV,CAAc,IAAnB,EAAyB,UAAU,CAAC,CAAD,CAAnC,EAAwC,KAAxC;;AACA,YAAI,EAAE,CAAF,GAAM,MAAV,EAAkB;AAChB,UAAA,KAAK,CAAC,KAAN,CAAY,IAAZ;AACD,SAFD,MAEO;AACL;AACD;AACF;AACF;;AACD,IAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD,GA9oBsB;AA+oBvB,EAAA,kBA/oBuB,8BA+oBJ,IA/oBI,EA+oBE,KA/oBF,EA+oBS;AAC9B,IAAA,cAAc,CAAC,KAAD,EAAQ,IAAI,CAAC,WAAb,CAAd;AACD,GAjpBsB;AAkpBvB,EAAA,eAlpBuB,2BAkpBP,IAlpBO,EAkpBD,KAlpBC,EAkpBM;AAC3B,QAAI,IAAI,CAAC,MAAT,EAAiB;AACf,UACE,QADF,GAII,IAJJ,CACE,QADF;AAAA,UAEE,QAFF,GAII,IAJJ,CAEE,QAFF;AAAA,UAGc,IAHd,GAII,IAJJ,CAGE,QAHF,CAGc,IAHd;AAKA,MAAA,KAAK,CAAC,KAAN,CAAY,QAAZ;AACA,UAAM,gBAAgB,GAAG,0BAA0B,CAAC,KAAD,EAAQ,QAAR,EAAkB,IAAlB,CAAnD;;AACA,UACE,CAAC,gBAAD,KACC,QAAQ,CAAC,MAAT,GAAkB,CAAlB,IACE,IAAI,CAAC,CAAD,CAAJ,KAAY,GAAZ,KACE,IAAI,CAAC,CAAD,CAAJ,KAAY,GAAZ,IAAmB,IAAI,CAAC,CAAD,CAAJ,KAAY,GADjC,KAEC,QAAQ,CAAC,MAFV,IAGC,QAAQ,CAAC,QAAT,CAAkB,CAAlB,MAAyB,QAH1B,KAIE,QAAQ,KAAK,GAAb,IAAoB,QAAQ,KAAK,GAJnC,CAFH,CADF,EAQE;AAEA,QAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD;;AACD,UAAI,gBAAJ,EAAsB;AACpB,QAAA,KAAK,CAAC,KAAN,CAAY,QAAQ,CAAC,MAAT,GAAkB,CAAlB,GAAsB,IAAtB,GAA6B,GAAzC;AACA,aAAK,IAAL,EAAW,QAAX,EAAqB,KAArB;AACA,QAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD,OAJD,MAIO;AACL,aAAK,IAAL,EAAW,QAAX,EAAqB,KAArB;AACD;AACF,KA3BD,MA2BO;AAEL,WAAK,IAAI,CAAC,QAAL,CAAc,IAAnB,EAAyB,IAAI,CAAC,QAA9B,EAAwC,KAAxC;AACA,MAAA,KAAK,CAAC,KAAN,CAAY,IAAI,CAAC,QAAjB;AACD;AACF,GAnrBsB;AAorBvB,EAAA,gBAprBuB,4BAorBN,IAprBM,EAorBA,KAprBA,EAorBO;AAE5B,QAAI,IAAI,CAAC,MAAT,EAAiB;AACf,MAAA,KAAK,CAAC,KAAN,CAAY,IAAI,CAAC,QAAjB;AACA,WAAK,IAAI,CAAC,QAAL,CAAc,IAAnB,EAAyB,IAAI,CAAC,QAA9B,EAAwC,KAAxC;AACD,KAHD,MAGO;AACL,WAAK,IAAI,CAAC,QAAL,CAAc,IAAnB,EAAyB,IAAI,CAAC,QAA9B,EAAwC,KAAxC;AACA,MAAA,KAAK,CAAC,KAAN,CAAY,IAAI,CAAC,QAAjB;AACD;AACF,GA7rBsB;AA8rBvB,EAAA,oBA9rBuB,gCA8rBF,IA9rBE,EA8rBI,KA9rBJ,EA8rBW;AAChC,SAAK,IAAI,CAAC,IAAL,CAAU,IAAf,EAAqB,IAAI,CAAC,IAA1B,EAAgC,KAAhC;AACA,IAAA,KAAK,CAAC,KAAN,CAAY,MAAM,IAAI,CAAC,QAAX,GAAsB,GAAlC;AACA,SAAK,IAAI,CAAC,KAAL,CAAW,IAAhB,EAAsB,IAAI,CAAC,KAA3B,EAAkC,KAAlC;AACD,GAlsBsB;AAmsBvB,EAAA,iBAnsBuB,6BAmsBL,IAnsBK,EAmsBC,KAnsBD,EAmsBQ;AAC7B,SAAK,IAAI,CAAC,IAAL,CAAU,IAAf,EAAqB,IAAI,CAAC,IAA1B,EAAgC,KAAhC;AACA,IAAA,KAAK,CAAC,KAAN,CAAY,KAAZ;AACA,SAAK,IAAI,CAAC,KAAL,CAAW,IAAhB,EAAsB,IAAI,CAAC,KAA3B,EAAkC,KAAlC;AACD,GAvsBsB;AAwsBvB,EAAA,gBAAgB,EAAG,gBAAgB,GAAG,0BAAU,IAAV,EAAgB,KAAhB,EAAuB;AAC3D,QAAM,IAAI,GAAG,IAAI,CAAC,QAAL,KAAkB,IAA/B;;AACA,QAAI,IAAJ,EAAU;AAER,MAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD;;AACD,IAAA,gBAAgB,CAAC,KAAD,EAAQ,IAAI,CAAC,IAAb,EAAmB,IAAnB,EAAyB,KAAzB,CAAhB;AACA,IAAA,KAAK,CAAC,KAAN,CAAY,MAAM,IAAI,CAAC,QAAX,GAAsB,GAAlC;AACA,IAAA,gBAAgB,CAAC,KAAD,EAAQ,IAAI,CAAC,KAAb,EAAoB,IAApB,EAA0B,IAA1B,CAAhB;;AACA,QAAI,IAAJ,EAAU;AACR,MAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD;AACF,GAptBsB;AAqtBvB,EAAA,iBAAiB,EAAE,gBArtBI;AAstBvB,EAAA,qBAttBuB,iCAstBD,IAttBC,EAstBK,KAttBL,EAstBY;AACjC,QAAQ,IAAR,GAAiB,IAAjB,CAAQ,IAAR;AACA,QAAM,UAAU,GAAG,KAAK,CAAC,qBAAN,CAA4B,IAAI,CAAC,IAAjC,CAAnB;;AACA,QACE,UAAU,KAAK,iBAAf,IACA,UAAU,IAAI,KAAK,CAAC,qBAAN,CAA4B,qBAF5C,EAGE;AACA,MAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACA,WAAK,IAAI,CAAC,IAAV,EAAgB,IAAhB,EAAsB,KAAtB;AACA,MAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD,KAPD,MAOO;AACL,WAAK,IAAI,CAAC,IAAV,EAAgB,IAAhB,EAAsB,KAAtB;AACD;;AACD,IAAA,KAAK,CAAC,KAAN,CAAY,KAAZ;AACA,SAAK,IAAI,CAAC,UAAL,CAAgB,IAArB,EAA2B,IAAI,CAAC,UAAhC,EAA4C,KAA5C;AACA,IAAA,KAAK,CAAC,KAAN,CAAY,KAAZ;AACA,SAAK,IAAI,CAAC,SAAL,CAAe,IAApB,EAA0B,IAAI,CAAC,SAA/B,EAA0C,KAA1C;AACD,GAvuBsB;AAwuBvB,EAAA,aAxuBuB,yBAwuBT,IAxuBS,EAwuBH,KAxuBG,EAwuBI;AACzB,IAAA,KAAK,CAAC,KAAN,CAAY,MAAZ;AACA,QAAM,UAAU,GAAG,KAAK,CAAC,qBAAN,CAA4B,IAAI,CAAC,MAAL,CAAY,IAAxC,CAAnB;;AACA,QACE,UAAU,KAAK,iBAAf,IACA,UAAU,GAAG,KAAK,CAAC,qBAAN,CAA4B,cADzC,IAEA,iBAAiB,CAAC,IAAI,CAAC,MAAN,CAHnB,EAIE;AACA,MAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACA,WAAK,IAAI,CAAC,MAAL,CAAY,IAAjB,EAAuB,IAAI,CAAC,MAA5B,EAAoC,KAApC;AACA,MAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD,KARD,MAQO;AACL,WAAK,IAAI,CAAC,MAAL,CAAY,IAAjB,EAAuB,IAAI,CAAC,MAA5B,EAAoC,KAApC;AACD;;AACD,IAAA,cAAc,CAAC,KAAD,EAAQ,IAAI,CAAC,WAAD,CAAZ,CAAd;AACD,GAvvBsB;AAwvBvB,EAAA,cAxvBuB,0BAwvBR,IAxvBQ,EAwvBF,KAxvBE,EAwvBK;AAC1B,QAAM,UAAU,GAAG,KAAK,CAAC,qBAAN,CAA4B,IAAI,CAAC,MAAL,CAAY,IAAxC,CAAnB;;AACA,QACE,UAAU,KAAK,iBAAf,IACA,UAAU,GAAG,KAAK,CAAC,qBAAN,CAA4B,cAF3C,EAGE;AACA,MAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACA,WAAK,IAAI,CAAC,MAAL,CAAY,IAAjB,EAAuB,IAAI,CAAC,MAA5B,EAAoC,KAApC;AACA,MAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD,KAPD,MAOO;AACL,WAAK,IAAI,CAAC,MAAL,CAAY,IAAjB,EAAuB,IAAI,CAAC,MAA5B,EAAoC,KAApC;AACD;;AACD,QAAI,IAAI,CAAC,QAAT,EAAmB;AACjB,MAAA,KAAK,CAAC,KAAN,CAAY,IAAZ;AACD;;AACD,IAAA,cAAc,CAAC,KAAD,EAAQ,IAAI,CAAC,WAAD,CAAZ,CAAd;AACD,GAxwBsB;AAywBvB,EAAA,eAzwBuB,2BAywBP,IAzwBO,EAywBD,KAzwBC,EAywBM;AAC3B,SAAK,IAAI,CAAC,UAAL,CAAgB,IAArB,EAA2B,IAAI,CAAC,UAAhC,EAA4C,KAA5C;AACD,GA3wBsB;AA4wBvB,EAAA,gBA5wBuB,4BA4wBN,IA5wBM,EA4wBA,KA5wBA,EA4wBO;AAC5B,QAAM,UAAU,GAAG,KAAK,CAAC,qBAAN,CAA4B,IAAI,CAAC,MAAL,CAAY,IAAxC,CAAnB;;AACA,QACE,UAAU,KAAK,iBAAf,IACA,UAAU,GAAG,KAAK,CAAC,qBAAN,CAA4B,gBAF3C,EAGE;AACA,MAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACA,WAAK,IAAI,CAAC,MAAL,CAAY,IAAjB,EAAuB,IAAI,CAAC,MAA5B,EAAoC,KAApC;AACA,MAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD,KAPD,MAOO;AACL,WAAK,IAAI,CAAC,MAAL,CAAY,IAAjB,EAAuB,IAAI,CAAC,MAA5B,EAAoC,KAApC;AACD;;AACD,QAAI,IAAI,CAAC,QAAT,EAAmB;AACjB,UAAI,IAAI,CAAC,QAAT,EAAmB;AACjB,QAAA,KAAK,CAAC,KAAN,CAAY,IAAZ;AACD;;AACD,MAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACA,WAAK,IAAI,CAAC,QAAL,CAAc,IAAnB,EAAyB,IAAI,CAAC,QAA9B,EAAwC,KAAxC;AACA,MAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD,KAPD,MAOO;AACL,UAAI,IAAI,CAAC,QAAT,EAAmB;AACjB,QAAA,KAAK,CAAC,KAAN,CAAY,IAAZ;AACD,OAFD,MAEO;AACL,QAAA,KAAK,CAAC,KAAN,CAAY,GAAZ;AACD;;AACD,WAAK,IAAI,CAAC,QAAL,CAAc,IAAnB,EAAyB,IAAI,CAAC,QAA9B,EAAwC,KAAxC;AACD;AACF,GAvyBsB;AAwyBvB,EAAA,YAxyBuB,wBAwyBV,IAxyBU,EAwyBJ,KAxyBI,EAwyBG;AACxB,IAAA,KAAK,CAAC,KAAN,CAAY,IAAI,CAAC,IAAL,CAAU,IAAV,GAAiB,GAAjB,GAAuB,IAAI,CAAC,QAAL,CAAc,IAAjD,EAAuD,IAAvD;AACD,GA1yBsB;AA2yBvB,EAAA,UA3yBuB,sBA2yBZ,IA3yBY,EA2yBN,KA3yBM,EA2yBC;AACtB,IAAA,KAAK,CAAC,KAAN,CAAY,IAAI,CAAC,IAAjB,EAAuB,IAAvB;AACD,GA7yBsB;AA8yBvB,EAAA,iBA9yBuB,6BA8yBL,IA9yBK,EA8yBC,KA9yBD,EA8yBQ;AAC7B,IAAA,KAAK,CAAC,KAAN,YAAgB,IAAI,CAAC,IAArB,GAA6B,IAA7B;AACD,GAhzBsB;AAizBvB,EAAA,OAjzBuB,mBAizBf,IAjzBe,EAizBT,KAjzBS,EAizBF;AACnB,QAAI,IAAI,CAAC,GAAL,IAAY,IAAhB,EAAsB;AAEpB,MAAA,KAAK,CAAC,KAAN,CAAY,IAAI,CAAC,GAAjB,EAAsB,IAAtB;AACD,KAHD,MAGO,IAAI,IAAI,CAAC,KAAL,IAAc,IAAlB,EAAwB;AAC7B,WAAK,aAAL,CAAmB,IAAnB,EAAyB,KAAzB;AACD,KAFM,MAEA,IAAI,IAAI,CAAC,MAAL,IAAe,IAAnB,EAAyB;AAC9B,MAAA,KAAK,CAAC,KAAN,CAAY,IAAI,CAAC,MAAL,GAAc,GAA1B,EAA+B,IAA/B;AACD,KAFM,MAEA;AACL,MAAA,KAAK,CAAC,KAAN,CAAY,SAAS,CAAC,IAAI,CAAC,KAAN,CAArB,EAAmC,IAAnC;AACD;AACF,GA5zBsB;AA6zBvB,EAAA,aA7zBuB,yBA6zBT,IA7zBS,EA6zBH,KA7zBG,EA6zBI;AACzB,QAAQ,KAAR,GAAkB,IAAlB,CAAQ,KAAR;AACA,IAAA,KAAK,CAAC,KAAN,YAAgB,KAAK,CAAC,OAAtB,cAAiC,KAAK,CAAC,KAAvC,GAAgD,IAAhD;AACD;AAh0BsB,CAAlB;;AAm0BP,IAAM,YAAY,GAAG,EAArB;AAKO,IAAM,aAAa,GAAG,SAAtB;;;IAED,K;AACJ,iBAAY,OAAZ,EAAqB;AAAA;;AACnB,QAAM,KAAK,GAAG,OAAO,IAAI,IAAX,GAAkB,YAAlB,GAAiC,OAA/C;AACA,SAAK,MAAL,GAAc,EAAd;;AAEA,QAAI,KAAK,CAAC,MAAN,IAAgB,IAApB,EAA0B;AACxB,WAAK,MAAL,GAAc,KAAK,CAAC,MAApB;AACA,WAAK,KAAL,GAAa,KAAK,aAAlB;AACD,KAHD,MAGO;AACL,WAAK,MAAL,GAAc,EAAd;AACD;;AACD,SAAK,SAAL,GAAiB,KAAK,CAAC,SAAN,IAAmB,IAAnB,GAA0B,KAAK,CAAC,SAAhC,GAA4C,SAA7D;AACA,SAAK,qBAAL,GACE,KAAK,CAAC,qBAAN,IAA+B,IAA/B,GACI,KAAK,CAAC,qBADV,GAEI,sBAHN;AAKA,SAAK,MAAL,GAAc,KAAK,CAAC,MAAN,IAAgB,IAAhB,GAAuB,KAAK,CAAC,MAA7B,GAAsC,IAApD;AACA,SAAK,OAAL,GAAe,KAAK,CAAC,OAAN,IAAiB,IAAjB,GAAwB,KAAK,CAAC,OAA9B,GAAwC,IAAvD;AACA,SAAK,WAAL,GACE,KAAK,CAAC,mBAAN,IAA6B,IAA7B,GAAoC,KAAK,CAAC,mBAA1C,GAAgE,CADlE;AAEA,SAAK,aAAL,GAAqB,KAAK,CAAC,QAAN,GAAiB,KAAK,CAAC,QAAvB,GAAkC,KAAvD;;AAEA,QAAI,KAAK,CAAC,SAAN,IAAmB,IAAvB,EAA6B;AAC3B,WAAK,KAAL,GACE,KAAK,CAAC,MAAN,IAAgB,IAAhB,GAAuB,KAAK,WAA5B,GAA0C,KAAK,mBADjD;AAEA,WAAK,SAAL,GAAiB,KAAK,CAAC,SAAvB;AACA,WAAK,IAAL,GAAY,CAAZ;AACA,WAAK,MAAL,GAAc,CAAd;AACA,WAAK,WAAL,GAAmB,KAAK,OAAL,CAAa,KAAb,CAAmB,IAAnB,EAAyB,MAAzB,GAAkC,CAArD;AACA,WAAK,OAAL,GAAe;AACb,QAAA,QAAQ,EAAE,IADG;AAGb,QAAA,SAAS,EAAE,IAHE;AAIb,QAAA,IAAI,EAAE,SAJO;AAKb,QAAA,MAAM,EAAE,KAAK,CAAC,SAAN,CAAgB,IAAhB,IAAwB,KAAK,CAAC,SAAN,CAAgB;AALnC,OAAf;AAOD;AACF;;;;WAED,eAAM,IAAN,EAAY;AACV,WAAK,MAAL,IAAe,IAAf;AACD;;;WAED,uBAAc,IAAd,EAAoB;AAClB,WAAK,MAAL,CAAY,KAAZ,CAAkB,IAAlB;AACD;;;WAED,qBAAY,IAAZ,EAAkB,IAAlB,EAAwB;AACtB,WAAK,MAAL,IAAe,IAAf;AACA,WAAK,GAAL,CAAS,IAAT,EAAe,IAAf;AACD;;;WAED,6BAAoB,IAApB,EAA0B,IAA1B,EAAgC;AAC9B,WAAK,MAAL,CAAY,KAAZ,CAAkB,IAAlB;AACA,WAAK,GAAL,CAAS,IAAT,EAAe,IAAf;AACD;;;WAED,aAAI,IAAJ,EAAU,IAAV,EAAgB;AACd,UAAI,IAAI,IAAI,IAAZ,EAAkB;AAChB,YAAQ,IAAR,GAAiB,IAAjB,CAAQ,IAAR;;AACA,YAAI,IAAI,CAAC,CAAD,CAAJ,KAAY,GAAZ,IAAmB,IAAI,CAAC,CAAD,CAAJ,KAAY,GAAnC,EAAwC;AAEtC,eAAK,MAAL,GAAc,CAAd;AACA,eAAK,IAAL;AACA;AACD;;AACD,YAAI,IAAI,CAAC,GAAL,IAAY,IAAhB,EAAsB;AACpB,cAAQ,OAAR,GAAoB,IAApB,CAAQ,OAAR;AACA,UAAA,OAAO,CAAC,QAAR,GAAmB,IAAI,CAAC,GAAL,CAAS,KAA5B;AACA,UAAA,OAAO,CAAC,IAAR,GAAe,IAAI,CAAC,IAApB;AACA,eAAK,SAAL,CAAe,UAAf,CAA0B,OAA1B;AACD;;AACD,YACG,IAAI,CAAC,CAAD,CAAJ,KAAY,GAAZ,IAAmB,IAAI,CAAC,CAAD,CAAJ,KAAY,GAAhC,IACC,IAAI,CAAC,CAAD,CAAJ,KAAY,GAAZ,IAAmB,IAAI,CAAC,CAAD,CAAJ,KAAY,GAA/B,IAAsC,OAAO,IAAI,CAAC,KAAZ,KAAsB,QAF/D,EAGE;AAEA,cAAQ,OAAR,GAAmB,IAAnB,CAAQ,MAAR;AACA,cAAM,MAAN,GAAuB,IAAvB,CAAM,MAAN;AAAA,cAAc,IAAd,GAAuB,IAAvB,CAAc,IAAd;;AACA,eAAK,IAAI,CAAC,GAAG,CAAb,EAAgB,CAAC,GAAG,OAApB,EAA4B,CAAC,EAA7B,EAAiC;AAC/B,gBAAI,IAAI,CAAC,CAAD,CAAJ,KAAY,IAAhB,EAAsB;AACpB,cAAA,MAAM,GAAG,CAAT;AACA,cAAA,IAAI;AACL,aAHD,MAGO;AACL,cAAA,MAAM;AACP;AACF;;AACD,eAAK,MAAL,GAAc,MAAd;AACA,eAAK,IAAL,GAAY,IAAZ;AACA;AACD;AACF;;AACD,UAAQ,MAAR,GAAmB,IAAnB,CAAQ,MAAR;AACA,UAAQ,OAAR,GAAoB,IAApB,CAAQ,OAAR;;AACA,UAAI,MAAM,GAAG,CAAb,EAAgB;AACd,YACE,KAAK,WAAL,GAAmB,CAAnB,KACC,OAAO,CAAC,MAAR,KAAmB,CAAnB,GACG,IAAI,CAAC,MAAM,GAAG,CAAV,CAAJ,KAAqB,OADxB,GAEG,IAAI,CAAC,QAAL,CAAc,OAAd,CAHJ,CADF,EAKE;AACA,eAAK,IAAL,IAAa,KAAK,WAAlB;AACA,eAAK,MAAL,GAAc,CAAd;AACD,SARD,MAQO;AACL,eAAK,MAAL,IAAe,MAAf;AACD;AACF;AACF;;;WAED,oBAAW;AACT,aAAO,KAAK,MAAZ;AACD;;;;;;AAGI,SAAS,QAAT,CAAkB,IAAlB,EAAwB,OAAxB,EAAiC;AAatC,MAAM,KAAK,GAAG,IAAI,KAAJ,CAAU,OAAV,CAAd;AAEA,EAAA,KAAK,CAAC,SAAN,CAAgB,IAAI,CAAC,IAArB,EAA2B,IAA3B,EAAiC,KAAjC;AACA,SAAO,KAAK,CAAC,MAAb;AACD", "file": "astring.js", "sourcesContent": ["// Astring is a tiny and fast JavaScript code generator from an ESTree-compliant AST.\n//\n// Astring was written by <PERSON> and released under an MIT license.\n//\n// The Git repository for Astring is available at:\n// https://github.com/davidbonnet/astring.git\n//\n// Please use the GitHub bug tracker to report issues:\n// https://github.com/davidbonnet/astring/issues\n\nconst { stringify } = JSON\n\n/* c8 ignore if */\nif (!String.prototype.repeat) {\n  /* c8 ignore next */\n  throw new Error(\n    'String.prototype.repeat is undefined, see https://github.com/davidbonnet/astring#installation',\n  )\n}\n\n/* c8 ignore if */\nif (!String.prototype.endsWith) {\n  /* c8 ignore next */\n  throw new Error(\n    'String.prototype.endsWith is undefined, see https://github.com/davidbonnet/astring#installation',\n  )\n}\n\nconst OPERATOR_PRECEDENCE = {\n  '||': 2,\n  '??': 3,\n  '&&': 4,\n  '|': 5,\n  '^': 6,\n  '&': 7,\n  '==': 8,\n  '!=': 8,\n  '===': 8,\n  '!==': 8,\n  '<': 9,\n  '>': 9,\n  '<=': 9,\n  '>=': 9,\n  in: 9,\n  instanceof: 9,\n  '<<': 10,\n  '>>': 10,\n  '>>>': 10,\n  '+': 11,\n  '-': 11,\n  '*': 12,\n  '%': 12,\n  '/': 12,\n  '**': 13,\n}\n\n// Enables parenthesis regardless of precedence\nexport const NEEDS_PARENTHESES = 17\n\nexport const EXPRESSIONS_PRECEDENCE = {\n  // Definitions\n  ArrayExpression: 20,\n  TaggedTemplateExpression: 20,\n  ThisExpression: 20,\n  Identifier: 20,\n  PrivateIdentifier: 20,\n  Literal: 18,\n  TemplateLiteral: 20,\n  Super: 20,\n  SequenceExpression: 20,\n  // Operations\n  MemberExpression: 19,\n  ChainExpression: 19,\n  CallExpression: 19,\n  NewExpression: 19,\n  // Other definitions\n  ArrowFunctionExpression: NEEDS_PARENTHESES,\n  ClassExpression: NEEDS_PARENTHESES,\n  FunctionExpression: NEEDS_PARENTHESES,\n  ObjectExpression: NEEDS_PARENTHESES,\n  // Other operations\n  UpdateExpression: 16,\n  UnaryExpression: 15,\n  AwaitExpression: 15,\n  BinaryExpression: 14,\n  LogicalExpression: 13,\n  ConditionalExpression: 4,\n  AssignmentExpression: 3,\n  YieldExpression: 2,\n  RestElement: 1,\n}\n\nfunction formatSequence(state, nodes) {\n  /*\n  Writes into `state` a sequence of `nodes`.\n  */\n  const { generator } = state\n  state.write('(')\n  if (nodes != null && nodes.length > 0) {\n    generator[nodes[0].type](nodes[0], state)\n    const { length } = nodes\n    for (let i = 1; i < length; i++) {\n      const param = nodes[i]\n      state.write(', ')\n      generator[param.type](param, state)\n    }\n  }\n  state.write(')')\n}\n\nfunction expressionNeedsParenthesis(state, node, parentNode, isRightHand) {\n  const nodePrecedence = state.expressionsPrecedence[node.type]\n  if (nodePrecedence === NEEDS_PARENTHESES) {\n    return true\n  }\n  const parentNodePrecedence = state.expressionsPrecedence[parentNode.type]\n  if (nodePrecedence !== parentNodePrecedence) {\n    // Different node types\n    return (\n      (!isRightHand &&\n        nodePrecedence === 15 &&\n        parentNodePrecedence === 14 &&\n        parentNode.operator === '**') ||\n      nodePrecedence < parentNodePrecedence\n    )\n  }\n  if (nodePrecedence !== 13 && nodePrecedence !== 14) {\n    // Not a `LogicalExpression` or `BinaryExpression`\n    return false\n  }\n  if (node.operator === '**' && parentNode.operator === '**') {\n    // Exponentiation operator has right-to-left associativity\n    return !isRightHand\n  }\n  if (\n    nodePrecedence === 13 &&\n    parentNodePrecedence === 13 &&\n    (node.operator === '??' || parentNode.operator === '??')\n  ) {\n    // Nullish coalescing and boolean operators cannot be combined\n    return true\n  }\n  if (isRightHand) {\n    // Parenthesis are used if both operators have the same precedence\n    return (\n      OPERATOR_PRECEDENCE[node.operator] <=\n      OPERATOR_PRECEDENCE[parentNode.operator]\n    )\n  }\n  return (\n    OPERATOR_PRECEDENCE[node.operator] <\n    OPERATOR_PRECEDENCE[parentNode.operator]\n  )\n}\n\nfunction formatExpression(state, node, parentNode, isRightHand) {\n  /*\n  Writes into `state` the provided `node`, adding parenthesis around if the provided `parentNode` needs it. If `node` is a right-hand argument, the provided `isRightHand` parameter should be `true`.\n  */\n  const { generator } = state\n  if (expressionNeedsParenthesis(state, node, parentNode, isRightHand)) {\n    state.write('(')\n    generator[node.type](node, state)\n    state.write(')')\n  } else {\n    generator[node.type](node, state)\n  }\n}\n\nfunction reindent(state, text, indent, lineEnd) {\n  /*\n  Writes into `state` the `text` string reindented with the provided `indent`.\n  */\n  const lines = text.split('\\n')\n  const end = lines.length - 1\n  state.write(lines[0].trim())\n  if (end > 0) {\n    state.write(lineEnd)\n    for (let i = 1; i < end; i++) {\n      state.write(indent + lines[i].trim() + lineEnd)\n    }\n    state.write(indent + lines[end].trim())\n  }\n}\n\nfunction formatComments(state, comments, indent, lineEnd) {\n  /*\n  Writes into `state` the provided list of `comments`, with the given `indent` and `lineEnd` strings.\n  Line comments will end with `\"\\n\"` regardless of the value of `lineEnd`.\n  Expects to start on a new unindented line.\n  */\n  const { length } = comments\n  for (let i = 0; i < length; i++) {\n    const comment = comments[i]\n    state.write(indent)\n    if (comment.type[0] === 'L') {\n      // Line comment\n      state.write('// ' + comment.value.trim() + '\\n', comment)\n    } else {\n      // Block comment\n      state.write('/*')\n      reindent(state, comment.value, indent, lineEnd)\n      state.write('*/' + lineEnd)\n    }\n  }\n}\n\nfunction hasCallExpression(node) {\n  /*\n  Returns `true` if the provided `node` contains a call expression and `false` otherwise.\n  */\n  let currentNode = node\n  while (currentNode != null) {\n    const { type } = currentNode\n    if (type[0] === 'C' && type[1] === 'a') {\n      // Is CallExpression\n      return true\n    } else if (type[0] === 'M' && type[1] === 'e' && type[2] === 'm') {\n      // Is MemberExpression\n      currentNode = currentNode.object\n    } else {\n      return false\n    }\n  }\n}\n\nfunction formatVariableDeclaration(state, node) {\n  /*\n  Writes into `state` a variable declaration.\n  */\n  const { generator } = state\n  const { declarations } = node\n  state.write(node.kind + ' ')\n  const { length } = declarations\n  if (length > 0) {\n    generator.VariableDeclarator(declarations[0], state)\n    for (let i = 1; i < length; i++) {\n      state.write(', ')\n      generator.VariableDeclarator(declarations[i], state)\n    }\n  }\n}\n\nlet ForInStatement,\n  FunctionDeclaration,\n  RestElement,\n  BinaryExpression,\n  ArrayExpression,\n  BlockStatement\n\nexport const GENERATOR = {\n  /*\n  Default generator.\n  */\n  Program(node, state) {\n    const indent = state.indent.repeat(state.indentLevel)\n    const { lineEnd, writeComments } = state\n    if (writeComments && node.comments != null) {\n      formatComments(state, node.comments, indent, lineEnd)\n    }\n    const statements = node.body\n    const { length } = statements\n    for (let i = 0; i < length; i++) {\n      const statement = statements[i]\n      if (writeComments && statement.comments != null) {\n        formatComments(state, statement.comments, indent, lineEnd)\n      }\n      state.write(indent)\n      this[statement.type](statement, state)\n      state.write(lineEnd)\n    }\n    if (writeComments && node.trailingComments != null) {\n      formatComments(state, node.trailingComments, indent, lineEnd)\n    }\n  },\n  BlockStatement: (BlockStatement = function (node, state) {\n    const indent = state.indent.repeat(state.indentLevel++)\n    const { lineEnd, writeComments } = state\n    const statementIndent = indent + state.indent\n    state.write('{')\n    const statements = node.body\n    if (statements != null && statements.length > 0) {\n      state.write(lineEnd)\n      if (writeComments && node.comments != null) {\n        formatComments(state, node.comments, statementIndent, lineEnd)\n      }\n      const { length } = statements\n      for (let i = 0; i < length; i++) {\n        const statement = statements[i]\n        if (writeComments && statement.comments != null) {\n          formatComments(state, statement.comments, statementIndent, lineEnd)\n        }\n        state.write(statementIndent)\n        this[statement.type](statement, state)\n        state.write(lineEnd)\n      }\n      state.write(indent)\n    } else {\n      if (writeComments && node.comments != null) {\n        state.write(lineEnd)\n        formatComments(state, node.comments, statementIndent, lineEnd)\n        state.write(indent)\n      }\n    }\n    if (writeComments && node.trailingComments != null) {\n      formatComments(state, node.trailingComments, statementIndent, lineEnd)\n    }\n    state.write('}')\n    state.indentLevel--\n  }),\n  ClassBody: BlockStatement,\n  StaticBlock(node, state) {\n    state.write('static ')\n    this.BlockStatement(node, state)\n  },\n  EmptyStatement(node, state) {\n    state.write(';')\n  },\n  ExpressionStatement(node, state) {\n    const precedence = state.expressionsPrecedence[node.expression.type]\n    if (\n      precedence === NEEDS_PARENTHESES ||\n      (precedence === 3 && node.expression.left.type[0] === 'O')\n    ) {\n      // Should always have parentheses or is an AssignmentExpression to an ObjectPattern\n      state.write('(')\n      this[node.expression.type](node.expression, state)\n      state.write(')')\n    } else {\n      this[node.expression.type](node.expression, state)\n    }\n    state.write(';')\n  },\n  IfStatement(node, state) {\n    state.write('if (')\n    this[node.test.type](node.test, state)\n    state.write(') ')\n    this[node.consequent.type](node.consequent, state)\n    if (node.alternate != null) {\n      state.write(' else ')\n      this[node.alternate.type](node.alternate, state)\n    }\n  },\n  LabeledStatement(node, state) {\n    this[node.label.type](node.label, state)\n    state.write(': ')\n    this[node.body.type](node.body, state)\n  },\n  BreakStatement(node, state) {\n    state.write('break')\n    if (node.label != null) {\n      state.write(' ')\n      this[node.label.type](node.label, state)\n    }\n    state.write(';')\n  },\n  ContinueStatement(node, state) {\n    state.write('continue')\n    if (node.label != null) {\n      state.write(' ')\n      this[node.label.type](node.label, state)\n    }\n    state.write(';')\n  },\n  WithStatement(node, state) {\n    state.write('with (')\n    this[node.object.type](node.object, state)\n    state.write(') ')\n    this[node.body.type](node.body, state)\n  },\n  SwitchStatement(node, state) {\n    const indent = state.indent.repeat(state.indentLevel++)\n    const { lineEnd, writeComments } = state\n    state.indentLevel++\n    const caseIndent = indent + state.indent\n    const statementIndent = caseIndent + state.indent\n    state.write('switch (')\n    this[node.discriminant.type](node.discriminant, state)\n    state.write(') {' + lineEnd)\n    const { cases: occurences } = node\n    const { length: occurencesCount } = occurences\n    for (let i = 0; i < occurencesCount; i++) {\n      const occurence = occurences[i]\n      if (writeComments && occurence.comments != null) {\n        formatComments(state, occurence.comments, caseIndent, lineEnd)\n      }\n      if (occurence.test) {\n        state.write(caseIndent + 'case ')\n        this[occurence.test.type](occurence.test, state)\n        state.write(':' + lineEnd)\n      } else {\n        state.write(caseIndent + 'default:' + lineEnd)\n      }\n      const { consequent } = occurence\n      const { length: consequentCount } = consequent\n      for (let i = 0; i < consequentCount; i++) {\n        const statement = consequent[i]\n        if (writeComments && statement.comments != null) {\n          formatComments(state, statement.comments, statementIndent, lineEnd)\n        }\n        state.write(statementIndent)\n        this[statement.type](statement, state)\n        state.write(lineEnd)\n      }\n    }\n    state.indentLevel -= 2\n    state.write(indent + '}')\n  },\n  ReturnStatement(node, state) {\n    state.write('return')\n    if (node.argument) {\n      state.write(' ')\n      this[node.argument.type](node.argument, state)\n    }\n    state.write(';')\n  },\n  ThrowStatement(node, state) {\n    state.write('throw ')\n    this[node.argument.type](node.argument, state)\n    state.write(';')\n  },\n  TryStatement(node, state) {\n    state.write('try ')\n    this[node.block.type](node.block, state)\n    if (node.handler) {\n      const { handler } = node\n      if (handler.param == null) {\n        state.write(' catch ')\n      } else {\n        state.write(' catch (')\n        this[handler.param.type](handler.param, state)\n        state.write(') ')\n      }\n      this[handler.body.type](handler.body, state)\n    }\n    if (node.finalizer) {\n      state.write(' finally ')\n      this[node.finalizer.type](node.finalizer, state)\n    }\n  },\n  WhileStatement(node, state) {\n    state.write('while (')\n    this[node.test.type](node.test, state)\n    state.write(') ')\n    this[node.body.type](node.body, state)\n  },\n  DoWhileStatement(node, state) {\n    state.write('do ')\n    this[node.body.type](node.body, state)\n    state.write(' while (')\n    this[node.test.type](node.test, state)\n    state.write(');')\n  },\n  ForStatement(node, state) {\n    state.write('for (')\n    if (node.init != null) {\n      const { init } = node\n      if (init.type[0] === 'V') {\n        formatVariableDeclaration(state, init)\n      } else {\n        this[init.type](init, state)\n      }\n    }\n    state.write('; ')\n    if (node.test) {\n      this[node.test.type](node.test, state)\n    }\n    state.write('; ')\n    if (node.update) {\n      this[node.update.type](node.update, state)\n    }\n    state.write(') ')\n    this[node.body.type](node.body, state)\n  },\n  ForInStatement: (ForInStatement = function (node, state) {\n    state.write(`for ${node.await ? 'await ' : ''}(`)\n    const { left } = node\n    if (left.type[0] === 'V') {\n      formatVariableDeclaration(state, left)\n    } else {\n      this[left.type](left, state)\n    }\n    // Identifying whether node.type is `ForInStatement` or `ForOfStatement`\n    state.write(node.type[3] === 'I' ? ' in ' : ' of ')\n    this[node.right.type](node.right, state)\n    state.write(') ')\n    this[node.body.type](node.body, state)\n  }),\n  ForOfStatement: ForInStatement,\n  DebuggerStatement(node, state) {\n    state.write('debugger;', node)\n  },\n  FunctionDeclaration: (FunctionDeclaration = function (node, state) {\n    state.write(\n      (node.async ? 'async ' : '') +\n        (node.generator ? 'function* ' : 'function ') +\n        (node.id ? node.id.name : ''),\n      node,\n    )\n    formatSequence(state, node.params)\n    state.write(' ')\n    this[node.body.type](node.body, state)\n  }),\n  FunctionExpression: FunctionDeclaration,\n  VariableDeclaration(node, state) {\n    formatVariableDeclaration(state, node)\n    state.write(';')\n  },\n  VariableDeclarator(node, state) {\n    this[node.id.type](node.id, state)\n    if (node.init != null) {\n      state.write(' = ')\n      this[node.init.type](node.init, state)\n    }\n  },\n  ClassDeclaration(node, state) {\n    state.write('class ' + (node.id ? `${node.id.name} ` : ''), node)\n    if (node.superClass) {\n      state.write('extends ')\n      const { superClass } = node\n      const { type } = superClass\n      const precedence = state.expressionsPrecedence[type]\n      if (\n        (type[0] !== 'C' || type[1] !== 'l' || type[5] !== 'E') &&\n        (precedence === NEEDS_PARENTHESES ||\n          precedence < state.expressionsPrecedence.ClassExpression)\n      ) {\n        // Not a ClassExpression that needs parentheses\n        state.write('(')\n        this[node.superClass.type](superClass, state)\n        state.write(')')\n      } else {\n        this[superClass.type](superClass, state)\n      }\n      state.write(' ')\n    }\n    this.ClassBody(node.body, state)\n  },\n  ImportDeclaration(node, state) {\n    state.write('import ')\n    const { specifiers, attributes } = node\n    const { length } = specifiers\n    // TODO: Once babili is fixed, put this after condition\n    // https://github.com/babel/babili/issues/430\n    let i = 0\n    if (length > 0) {\n      for (; i < length; ) {\n        if (i > 0) {\n          state.write(', ')\n        }\n        const specifier = specifiers[i]\n        const type = specifier.type[6]\n        if (type === 'D') {\n          // ImportDefaultSpecifier\n          state.write(specifier.local.name, specifier)\n          i++\n        } else if (type === 'N') {\n          // ImportNamespaceSpecifier\n          state.write('* as ' + specifier.local.name, specifier)\n          i++\n        } else {\n          // ImportSpecifier\n          break\n        }\n      }\n      if (i < length) {\n        state.write('{')\n        for (;;) {\n          const specifier = specifiers[i]\n          const { name } = specifier.imported\n          state.write(name, specifier)\n          if (name !== specifier.local.name) {\n            state.write(' as ' + specifier.local.name)\n          }\n          if (++i < length) {\n            state.write(', ')\n          } else {\n            break\n          }\n        }\n        state.write('}')\n      }\n      state.write(' from ')\n    }\n    this.Literal(node.source, state)\n\n    if (attributes && attributes.length > 0) {\n      state.write(' with { ')\n      for (let i = 0; i < attributes.length; i++) {\n        this.ImportAttribute(attributes[i], state)\n        if (i < attributes.length - 1) state.write(', ')\n      }\n\n      state.write(' }')\n    }\n    state.write(';')\n  },\n  ImportAttribute(node, state) {\n    this.Identifier(node.key, state)\n    state.write(': ')\n    this.Literal(node.value, state)\n  },\n  ImportExpression(node, state) {\n    state.write('import(')\n    this[node.source.type](node.source, state)\n    state.write(')')\n  },\n  ExportDefaultDeclaration(node, state) {\n    state.write('export default ')\n    this[node.declaration.type](node.declaration, state)\n    if (\n      state.expressionsPrecedence[node.declaration.type] != null &&\n      node.declaration.type[0] !== 'F'\n    ) {\n      // All expression nodes except `FunctionExpression`\n      state.write(';')\n    }\n  },\n  ExportNamedDeclaration(node, state) {\n    state.write('export ')\n    if (node.declaration) {\n      this[node.declaration.type](node.declaration, state)\n    } else {\n      state.write('{')\n      const { specifiers } = node,\n        { length } = specifiers\n      if (length > 0) {\n        for (let i = 0; ; ) {\n          const specifier = specifiers[i]\n          const { name } = specifier.local\n          state.write(name, specifier)\n          if (name !== specifier.exported.name) {\n            state.write(' as ' + specifier.exported.name)\n          }\n          if (++i < length) {\n            state.write(', ')\n          } else {\n            break\n          }\n        }\n      }\n      state.write('}')\n      if (node.source) {\n        state.write(' from ')\n        this.Literal(node.source, state)\n      }\n\n      if (node.attributes && node.attributes.length > 0) {\n        state.write(' with { ')\n        for (let i = 0; i < node.attributes.length; i++) {\n          this.ImportAttribute(node.attributes[i], state)\n          if (i < node.attributes.length - 1) state.write(', ')\n        }\n\n        state.write(' }')\n      }\n\n      state.write(';')\n    }\n  },\n  ExportAllDeclaration(node, state) {\n    if (node.exported != null) {\n      state.write('export * as ' + node.exported.name + ' from ')\n    } else {\n      state.write('export * from ')\n    }\n    this.Literal(node.source, state)\n\n    if (node.attributes && node.attributes.length > 0) {\n      state.write(' with { ')\n      for (let i = 0; i < node.attributes.length; i++) {\n        this.ImportAttribute(node.attributes[i], state)\n        if (i < node.attributes.length - 1) state.write(', ')\n      }\n\n      state.write(' }')\n    }\n\n    state.write(';')\n  },\n  MethodDefinition(node, state) {\n    if (node.static) {\n      state.write('static ')\n    }\n    const kind = node.kind[0]\n    if (kind === 'g' || kind === 's') {\n      // Getter or setter\n      state.write(node.kind + ' ')\n    }\n    if (node.value.async) {\n      state.write('async ')\n    }\n    if (node.value.generator) {\n      state.write('*')\n    }\n    if (node.computed) {\n      state.write('[')\n      this[node.key.type](node.key, state)\n      state.write(']')\n    } else {\n      this[node.key.type](node.key, state)\n    }\n    formatSequence(state, node.value.params)\n    state.write(' ')\n    this[node.value.body.type](node.value.body, state)\n  },\n  ClassExpression(node, state) {\n    this.ClassDeclaration(node, state)\n  },\n  ArrowFunctionExpression(node, state) {\n    state.write(node.async ? 'async ' : '', node)\n    const { params } = node\n    if (params != null) {\n      // Omit parenthesis if only one named parameter\n      if (params.length === 1 && params[0].type[0] === 'I') {\n        // If params[0].type[0] starts with 'I', it can't be `ImportDeclaration` nor `IfStatement` and thus is `Identifier`\n        state.write(params[0].name, params[0])\n      } else {\n        formatSequence(state, node.params)\n      }\n    }\n    state.write(' => ')\n    if (node.body.type[0] === 'O') {\n      // Body is an object expression\n      state.write('(')\n      this.ObjectExpression(node.body, state)\n      state.write(')')\n    } else {\n      this[node.body.type](node.body, state)\n    }\n  },\n  ThisExpression(node, state) {\n    state.write('this', node)\n  },\n  Super(node, state) {\n    state.write('super', node)\n  },\n  RestElement: (RestElement = function (node, state) {\n    state.write('...')\n    this[node.argument.type](node.argument, state)\n  }),\n  SpreadElement: RestElement,\n  YieldExpression(node, state) {\n    state.write(node.delegate ? 'yield*' : 'yield')\n    if (node.argument) {\n      state.write(' ')\n      this[node.argument.type](node.argument, state)\n    }\n  },\n  AwaitExpression(node, state) {\n    state.write('await ', node)\n    formatExpression(state, node.argument, node)\n  },\n  TemplateLiteral(node, state) {\n    const { quasis, expressions } = node\n    state.write('`')\n    const { length } = expressions\n    for (let i = 0; i < length; i++) {\n      const expression = expressions[i]\n      const quasi = quasis[i]\n      state.write(quasi.value.raw, quasi)\n      state.write('${')\n      this[expression.type](expression, state)\n      state.write('}')\n    }\n    const quasi = quasis[quasis.length - 1]\n    state.write(quasi.value.raw, quasi)\n    state.write('`')\n  },\n  TemplateElement(node, state) {\n    state.write(node.value.raw, node)\n  },\n  TaggedTemplateExpression(node, state) {\n    formatExpression(state, node.tag, node)\n    this[node.quasi.type](node.quasi, state)\n  },\n  ArrayExpression: (ArrayExpression = function (node, state) {\n    state.write('[')\n    if (node.elements.length > 0) {\n      const { elements } = node,\n        { length } = elements\n      for (let i = 0; ; ) {\n        const element = elements[i]\n        if (element != null) {\n          this[element.type](element, state)\n        }\n        if (++i < length) {\n          state.write(', ')\n        } else {\n          if (element == null) {\n            state.write(', ')\n          }\n          break\n        }\n      }\n    }\n    state.write(']')\n  }),\n  ArrayPattern: ArrayExpression,\n  ObjectExpression(node, state) {\n    const indent = state.indent.repeat(state.indentLevel++)\n    const { lineEnd, writeComments } = state\n    const propertyIndent = indent + state.indent\n    state.write('{')\n    if (node.properties.length > 0) {\n      state.write(lineEnd)\n      if (writeComments && node.comments != null) {\n        formatComments(state, node.comments, propertyIndent, lineEnd)\n      }\n      const comma = ',' + lineEnd\n      const { properties } = node,\n        { length } = properties\n      for (let i = 0; ; ) {\n        const property = properties[i]\n        if (writeComments && property.comments != null) {\n          formatComments(state, property.comments, propertyIndent, lineEnd)\n        }\n        state.write(propertyIndent)\n        this[property.type](property, state)\n        if (++i < length) {\n          state.write(comma)\n        } else {\n          break\n        }\n      }\n      state.write(lineEnd)\n      if (writeComments && node.trailingComments != null) {\n        formatComments(state, node.trailingComments, propertyIndent, lineEnd)\n      }\n      state.write(indent + '}')\n    } else if (writeComments) {\n      if (node.comments != null) {\n        state.write(lineEnd)\n        formatComments(state, node.comments, propertyIndent, lineEnd)\n        if (node.trailingComments != null) {\n          formatComments(state, node.trailingComments, propertyIndent, lineEnd)\n        }\n        state.write(indent + '}')\n      } else if (node.trailingComments != null) {\n        state.write(lineEnd)\n        formatComments(state, node.trailingComments, propertyIndent, lineEnd)\n        state.write(indent + '}')\n      } else {\n        state.write('}')\n      }\n    } else {\n      state.write('}')\n    }\n    state.indentLevel--\n  },\n  Property(node, state) {\n    if (node.method || node.kind[0] !== 'i') {\n      // Either a method or of kind `set` or `get` (not `init`)\n      this.MethodDefinition(node, state)\n    } else {\n      if (!node.shorthand) {\n        if (node.computed) {\n          state.write('[')\n          this[node.key.type](node.key, state)\n          state.write(']')\n        } else {\n          this[node.key.type](node.key, state)\n        }\n        state.write(': ')\n      }\n      this[node.value.type](node.value, state)\n    }\n  },\n  PropertyDefinition(node, state) {\n    if (node.static) {\n      state.write('static ')\n    }\n    if (node.computed) {\n      state.write('[')\n    }\n    this[node.key.type](node.key, state)\n    if (node.computed) {\n      state.write(']')\n    }\n    if (node.value == null) {\n      if (node.key.type[0] !== 'F') {\n        state.write(';')\n      }\n      return\n    }\n    state.write(' = ')\n    this[node.value.type](node.value, state)\n    state.write(';')\n  },\n  ObjectPattern(node, state) {\n    state.write('{')\n    if (node.properties.length > 0) {\n      const { properties } = node,\n        { length } = properties\n      for (let i = 0; ; ) {\n        this[properties[i].type](properties[i], state)\n        if (++i < length) {\n          state.write(', ')\n        } else {\n          break\n        }\n      }\n    }\n    state.write('}')\n  },\n  SequenceExpression(node, state) {\n    formatSequence(state, node.expressions)\n  },\n  UnaryExpression(node, state) {\n    if (node.prefix) {\n      const {\n        operator,\n        argument,\n        argument: { type },\n      } = node\n      state.write(operator)\n      const needsParentheses = expressionNeedsParenthesis(state, argument, node)\n      if (\n        !needsParentheses &&\n        (operator.length > 1 ||\n          (type[0] === 'U' &&\n            (type[1] === 'n' || type[1] === 'p') &&\n            argument.prefix &&\n            argument.operator[0] === operator &&\n            (operator === '+' || operator === '-')))\n      ) {\n        // Large operator or argument is UnaryExpression or UpdateExpression node\n        state.write(' ')\n      }\n      if (needsParentheses) {\n        state.write(operator.length > 1 ? ' (' : '(')\n        this[type](argument, state)\n        state.write(')')\n      } else {\n        this[type](argument, state)\n      }\n    } else {\n      // FIXME: This case never occurs\n      this[node.argument.type](node.argument, state)\n      state.write(node.operator)\n    }\n  },\n  UpdateExpression(node, state) {\n    // Always applied to identifiers or members, no parenthesis check needed\n    if (node.prefix) {\n      state.write(node.operator)\n      this[node.argument.type](node.argument, state)\n    } else {\n      this[node.argument.type](node.argument, state)\n      state.write(node.operator)\n    }\n  },\n  AssignmentExpression(node, state) {\n    this[node.left.type](node.left, state)\n    state.write(' ' + node.operator + ' ')\n    this[node.right.type](node.right, state)\n  },\n  AssignmentPattern(node, state) {\n    this[node.left.type](node.left, state)\n    state.write(' = ')\n    this[node.right.type](node.right, state)\n  },\n  BinaryExpression: (BinaryExpression = function (node, state) {\n    const isIn = node.operator === 'in'\n    if (isIn) {\n      // Avoids confusion in `for` loops initializers\n      state.write('(')\n    }\n    formatExpression(state, node.left, node, false)\n    state.write(' ' + node.operator + ' ')\n    formatExpression(state, node.right, node, true)\n    if (isIn) {\n      state.write(')')\n    }\n  }),\n  LogicalExpression: BinaryExpression,\n  ConditionalExpression(node, state) {\n    const { test } = node\n    const precedence = state.expressionsPrecedence[test.type]\n    if (\n      precedence === NEEDS_PARENTHESES ||\n      precedence <= state.expressionsPrecedence.ConditionalExpression\n    ) {\n      state.write('(')\n      this[test.type](test, state)\n      state.write(')')\n    } else {\n      this[test.type](test, state)\n    }\n    state.write(' ? ')\n    this[node.consequent.type](node.consequent, state)\n    state.write(' : ')\n    this[node.alternate.type](node.alternate, state)\n  },\n  NewExpression(node, state) {\n    state.write('new ')\n    const precedence = state.expressionsPrecedence[node.callee.type]\n    if (\n      precedence === NEEDS_PARENTHESES ||\n      precedence < state.expressionsPrecedence.CallExpression ||\n      hasCallExpression(node.callee)\n    ) {\n      state.write('(')\n      this[node.callee.type](node.callee, state)\n      state.write(')')\n    } else {\n      this[node.callee.type](node.callee, state)\n    }\n    formatSequence(state, node['arguments'])\n  },\n  CallExpression(node, state) {\n    const precedence = state.expressionsPrecedence[node.callee.type]\n    if (\n      precedence === NEEDS_PARENTHESES ||\n      precedence < state.expressionsPrecedence.CallExpression\n    ) {\n      state.write('(')\n      this[node.callee.type](node.callee, state)\n      state.write(')')\n    } else {\n      this[node.callee.type](node.callee, state)\n    }\n    if (node.optional) {\n      state.write('?.')\n    }\n    formatSequence(state, node['arguments'])\n  },\n  ChainExpression(node, state) {\n    this[node.expression.type](node.expression, state)\n  },\n  MemberExpression(node, state) {\n    const precedence = state.expressionsPrecedence[node.object.type]\n    if (\n      precedence === NEEDS_PARENTHESES ||\n      precedence < state.expressionsPrecedence.MemberExpression\n    ) {\n      state.write('(')\n      this[node.object.type](node.object, state)\n      state.write(')')\n    } else {\n      this[node.object.type](node.object, state)\n    }\n    if (node.computed) {\n      if (node.optional) {\n        state.write('?.')\n      }\n      state.write('[')\n      this[node.property.type](node.property, state)\n      state.write(']')\n    } else {\n      if (node.optional) {\n        state.write('?.')\n      } else {\n        state.write('.')\n      }\n      this[node.property.type](node.property, state)\n    }\n  },\n  MetaProperty(node, state) {\n    state.write(node.meta.name + '.' + node.property.name, node)\n  },\n  Identifier(node, state) {\n    state.write(node.name, node)\n  },\n  PrivateIdentifier(node, state) {\n    state.write(`#${node.name}`, node)\n  },\n  Literal(node, state) {\n    if (node.raw != null) {\n      // Non-standard property\n      state.write(node.raw, node)\n    } else if (node.regex != null) {\n      this.RegExpLiteral(node, state)\n    } else if (node.bigint != null) {\n      state.write(node.bigint + 'n', node)\n    } else {\n      state.write(stringify(node.value), node)\n    }\n  },\n  RegExpLiteral(node, state) {\n    const { regex } = node\n    state.write(`/${regex.pattern}/${regex.flags}`, node)\n  },\n}\n\nconst EMPTY_OBJECT = {}\n\n/*\nDEPRECATED: Alternate export of `GENERATOR`.\n*/\nexport const baseGenerator = GENERATOR\n\nclass State {\n  constructor(options) {\n    const setup = options == null ? EMPTY_OBJECT : options\n    this.output = ''\n    // Functional options\n    if (setup.output != null) {\n      this.output = setup.output\n      this.write = this.writeToStream\n    } else {\n      this.output = ''\n    }\n    this.generator = setup.generator != null ? setup.generator : GENERATOR\n    this.expressionsPrecedence =\n      setup.expressionsPrecedence != null\n        ? setup.expressionsPrecedence\n        : EXPRESSIONS_PRECEDENCE\n    // Formating setup\n    this.indent = setup.indent != null ? setup.indent : '  '\n    this.lineEnd = setup.lineEnd != null ? setup.lineEnd : '\\n'\n    this.indentLevel =\n      setup.startingIndentLevel != null ? setup.startingIndentLevel : 0\n    this.writeComments = setup.comments ? setup.comments : false\n    // Source map\n    if (setup.sourceMap != null) {\n      this.write =\n        setup.output == null ? this.writeAndMap : this.writeToStreamAndMap\n      this.sourceMap = setup.sourceMap\n      this.line = 1\n      this.column = 0\n      this.lineEndSize = this.lineEnd.split('\\n').length - 1\n      this.mapping = {\n        original: null,\n        // Uses the entire state to avoid generating ephemeral objects\n        generated: this,\n        name: undefined,\n        source: setup.sourceMap.file || setup.sourceMap._file,\n      }\n    }\n  }\n\n  write(code) {\n    this.output += code\n  }\n\n  writeToStream(code) {\n    this.output.write(code)\n  }\n\n  writeAndMap(code, node) {\n    this.output += code\n    this.map(code, node)\n  }\n\n  writeToStreamAndMap(code, node) {\n    this.output.write(code)\n    this.map(code, node)\n  }\n\n  map(code, node) {\n    if (node != null) {\n      const { type } = node\n      if (type[0] === 'L' && type[2] === 'n') {\n        // LineComment\n        this.column = 0\n        this.line++\n        return\n      }\n      if (node.loc != null) {\n        const { mapping } = this\n        mapping.original = node.loc.start\n        mapping.name = node.name\n        this.sourceMap.addMapping(mapping)\n      }\n      if (\n        (type[0] === 'T' && type[8] === 'E') ||\n        (type[0] === 'L' && type[1] === 'i' && typeof node.value === 'string')\n      ) {\n        // TemplateElement or Literal string node\n        const { length } = code\n        let { column, line } = this\n        for (let i = 0; i < length; i++) {\n          if (code[i] === '\\n') {\n            column = 0\n            line++\n          } else {\n            column++\n          }\n        }\n        this.column = column\n        this.line = line\n        return\n      }\n    }\n    const { length } = code\n    const { lineEnd } = this\n    if (length > 0) {\n      if (\n        this.lineEndSize > 0 &&\n        (lineEnd.length === 1\n          ? code[length - 1] === lineEnd\n          : code.endsWith(lineEnd))\n      ) {\n        this.line += this.lineEndSize\n        this.column = 0\n      } else {\n        this.column += length\n      }\n    }\n  }\n\n  toString() {\n    return this.output\n  }\n}\n\nexport function generate(node, options) {\n  /*\n  Returns a string representing the rendered code of the provided AST `node`.\n  The `options` are:\n\n  - `indent`: string to use for indentation (defaults to `␣␣`)\n  - `lineEnd`: string to use for line endings (defaults to `\\n`)\n  - `startingIndentLevel`: indent level to start from (defaults to `0`)\n  - `comments`: generate comments if `true` (defaults to `false`)\n  - `output`: output stream to write the rendered code to (defaults to `null`)\n  - `generator`: custom code generator (defaults to `GENERATOR`)\n  - `expressionsPrecedence`: custom map of node types and their precedence level (defaults to `EXPRESSIONS_PRECEDENCE`)\n  */\n  const state = new State(options)\n  // Travel through the AST node and generate the code\n  state.generator[node.type](node, state)\n  return state.output\n}\n"]}