{"main": "dist/vanilla-extract-css-disableRuntimeStyles.cjs.js", "module": "dist/vanilla-extract-css-disableRuntimeStyles.esm.js", "browser": {"./dist/vanilla-extract-css-disableRuntimeStyles.cjs.js": "./dist/vanilla-extract-css-disableRuntimeStyles.browser.cjs.js", "./dist/vanilla-extract-css-disableRuntimeStyles.esm.js": "./dist/vanilla-extract-css-disableRuntimeStyles.browser.esm.js"}, "exports": {"./package.json": "./package.json", ".": {"browser": {"module": "./dist/vanilla-extract-css-disableRuntimeStyles.browser.esm.js", "default": "./dist/vanilla-extract-css-disableRuntimeStyles.browser.cjs.js"}, "module": "./dist/vanilla-extract-css-disableRuntimeStyles.esm.js", "default": "./dist/vanilla-extract-css-disableRuntimeStyles.cjs.js"}}}