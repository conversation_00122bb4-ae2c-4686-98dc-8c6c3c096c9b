{"version": 3, "file": "index.d.ts", "sourceRoot": "https://raw.githubusercontent.com/fb55/htmlparser2/e2939a6c7b05d5c4845b4a2e458a4fc0a65a321d/src/", "sources": ["index.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,EAAE,KAAK,aAAa,EAAE,MAAM,aAAa,CAAC;AACzD,YAAY,EAAE,OAAO,EAAE,aAAa,EAAE,MAAM,aAAa,CAAC;AAC1D,OAAO,EAAE,MAAM,EAAE,MAAM,aAAa,CAAC;AAErC,OAAO,EAEH,KAAK,iBAAiB,EACtB,KAAK,SAAS,EACd,KAAK,OAAO,EACZ,KAAK,QAAQ,EAChB,MAAM,YAAY,CAAC;AAEpB,OAAO,EACH,UAAU,EAEV,UAAU,IAAI,cAAc,EAC5B,KAAK,iBAAiB,GACzB,MAAM,YAAY,CAAC;AAEpB,MAAM,MAAM,OAAO,GAAG,aAAa,GAAG,iBAAiB,CAAC;AAIxD;;;;;GAKG;AACH,wBAAgB,aAAa,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,OAAO,GAAG,QAAQ,CAIvE;AACD;;;;;;;;;GASG;AACH,wBAAgB,QAAQ,CAAC,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,OAAO,GAAG,SAAS,EAAE,CAErE;AACD;;;;;;GAMG;AACH,wBAAgB,oBAAoB,CAChC,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI,EAAE,QAAQ,EAAE,QAAQ,KAAK,IAAI,EAC3D,OAAO,CAAC,EAAE,OAAO,EACjB,eAAe,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,KAAK,IAAI,GAC7C,MAAM,CAOR;AACD;;;;;;;GAOG;AACH,wBAAgB,eAAe,CAC3B,QAAQ,EAAE,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI,EAAE,GAAG,EAAE,SAAS,EAAE,KAAK,IAAI,EACzD,OAAO,CAAC,EAAE,OAAO,EACjB,eAAe,CAAC,EAAE,CAAC,OAAO,EAAE,OAAO,KAAK,IAAI,GAC7C,MAAM,CAGR;AAED,OAAO,EACH,OAAO,IAAI,SAAS,EACpB,KAAK,SAAS,IAAI,kBAAkB,EACpC,SAAS,GACZ,MAAM,gBAAgB,CAAC;AAMxB,OAAO,KAAK,WAAW,MAAM,gBAAgB,CAAC;AAE9C,OAAO,EAAW,KAAK,IAAI,EAAE,MAAM,UAAU,CAAC;AAE9C,OAAO,EAAE,OAAO,EAAE,KAAK,IAAI,EAAE,MAAM,UAAU,CAAC;AAI9C;;;;;GAKG;AACH,wBAAgB,SAAS,CACrB,IAAI,EAAE,MAAM,EACZ,OAAO,GAAE,OAAiC,GAC3C,IAAI,GAAG,IAAI,CAEb;AAED,OAAO,KAAK,QAAQ,MAAM,UAAU,CAAC"}