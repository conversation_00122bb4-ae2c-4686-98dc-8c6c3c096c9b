{"name": "htmlparser2", "description": "Fast & forgiving HTML/XML parser", "version": "9.1.0", "author": "<PERSON> <<EMAIL>>", "funding": ["https://github.com/fb55/htmlparser2?sponsor=1", {"type": "github", "url": "https://github.com/sponsors/fb55"}], "license": "MIT", "sideEffects": false, "keywords": ["html", "parser", "streams", "xml", "dom", "rss", "feed", "atom"], "repository": {"type": "git", "url": "git://github.com/fb55/htmlparser2.git"}, "directories": {"lib": "lib/"}, "main": "lib/index.js", "types": "lib/index.d.ts", "module": "lib/esm/index.js", "exports": {".": {"require": "./lib/index.js", "import": "./lib/esm/index.js"}, "./lib/WritableStream": {"require": "./lib/WritableStream.js", "import": "./lib/esm/WritableStream.js"}}, "files": ["lib/**/*"], "scripts": {"test": "npm run test:jest && npm run lint", "test:jest": "jest", "lint": "npm run lint:es && npm run lint:prettier", "lint:es": "eslint src", "lint:prettier": "npm run format:prettier:raw -- --check", "format": "npm run format:es && npm run format:prettier", "format:es": "npm run lint:es -- --fix", "format:prettier": "npm run format:prettier:raw -- --write", "format:prettier:raw": "prettier '**/*.{ts,md,json,yml}'", "build": "npm run build:cjs && npm run build:esm", "build:cjs": "tsc --sourceRoot https://raw.githubusercontent.com/fb55/htmlparser2/$(git rev-parse HEAD)/src/", "build:esm": "npm run build:cjs -- --module esnext --target es2019 --outDir lib/esm && echo '{\"type\":\"module\"}' > lib/esm/package.json", "prepare": "npm run build"}, "dependencies": {"domelementtype": "^2.3.0", "domhandler": "^5.0.3", "domutils": "^3.1.0", "entities": "^4.5.0"}, "devDependencies": {"@types/jest": "^29.5.11", "@types/node": "^20.10.6", "@typescript-eslint/eslint-plugin": "^6.17.0", "@typescript-eslint/parser": "^6.17.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-n": "^16.6.1", "eslint-plugin-unicorn": "^50.0.1", "jest": "^29.7.0", "prettier": "^3.1.1", "ts-jest": "^29.1.1", "typescript": "^5.3.3"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "coverageProvider": "v8", "moduleNameMapper": {"^(.*)\\.js$": ["$1", "$1.js"]}}, "prettier": {"tabWidth": 4}}