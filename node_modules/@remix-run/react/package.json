{"name": "@remix-run/react", "version": "2.16.7", "description": "React DOM bindings for Remix", "bugs": {"url": "https://github.com/remix-run/remix/issues"}, "repository": {"type": "git", "url": "https://github.com/remix-run/remix", "directory": "packages/remix-react"}, "license": "MIT", "sideEffects": false, "main": "dist/index.js", "typings": "dist/index.d.ts", "module": "dist/esm/index.js", "dependencies": {"@remix-run/router": "1.23.0", "react-router": "6.30.0", "react-router-dom": "6.30.0", "turbo-stream": "2.4.1", "@remix-run/server-runtime": "2.16.7"}, "devDependencies": {"@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.3.0", "@types/react": "^18.2.20", "jest-environment-jsdom": "^29.6.4", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.1.6", "@remix-run/node": "2.16.7", "@remix-run/react": "2.16.7"}, "peerDependencies": {"react": "^18.0.0", "react-dom": "^18.0.0", "typescript": "^5.1.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "engines": {"node": ">=18.0.0"}, "files": ["dist/", "future/", "CHANGELOG.md", "LICENSE.md", "README.md"], "scripts": {"tsc": "tsc"}}