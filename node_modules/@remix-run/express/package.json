{"name": "@remix-run/express", "version": "2.16.7", "description": "Express server request handler for Remix", "bugs": {"url": "https://github.com/remix-run/remix/issues"}, "repository": {"type": "git", "url": "https://github.com/remix-run/remix", "directory": "packages/remix-express"}, "license": "MIT", "main": "dist/index.js", "typings": "dist/index.d.ts", "dependencies": {"@remix-run/node": "2.16.7"}, "devDependencies": {"@types/express": "^4.17.9", "@types/node": "^18.17.1", "@types/supertest": "^2.0.10", "express": "^4.20.0", "node-mocks-http": "^1.10.1", "supertest": "^6.3.3", "typescript": "^5.1.6"}, "peerDependencies": {"express": "^4.20.0", "typescript": "^5.1.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}, "engines": {"node": ">=18.0.0"}, "files": ["dist/", "CHANGELOG.md", "LICENSE.md", "README.md"], "scripts": {"tsc": "tsc"}}