/**
 * @remix-run/express v2.16.7
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var node = require('@remix-run/node');

// IDK why this is needed when it's in the tsconfig..........

/**
 * A function that returns the value to use as `context` in route `loader` and
 * `action` functions.
 *
 * You can think of this as an escape hatch that allows you to pass
 * environment/platform-specific values through to your loader/action, such as
 * values that are generated by Express middleware like `req.session`.
 */

/**
 * Returns a request handler for Express that serves the response using Remix.
 */
function createRequestHandler({
  build,
  getLoadContext,
  mode = process.env.NODE_ENV
}) {
  let handleRequest = node.createRequestHandler(build, mode);
  return async (req, res, next) => {
    try {
      let request = createRemixRequest(req, res);
      let loadContext = await (getLoadContext === null || getLoadContext === void 0 ? void 0 : getLoadContext(req, res));
      let response = await handleRequest(request, loadContext);
      await sendRemixResponse(res, response);
    } catch (error) {
      // Express doesn't support async functions, so we have to pass along the
      // error manually using next().
      next(error);
    }
  };
}
function createRemixHeaders(requestHeaders) {
  let headers = new Headers();
  for (let [key, values] of Object.entries(requestHeaders)) {
    if (values) {
      if (Array.isArray(values)) {
        for (let value of values) {
          headers.append(key, value);
        }
      } else {
        headers.set(key, values);
      }
    }
  }
  return headers;
}
function createRemixRequest(req, res) {
  var _req$get, _req$get2;
  // req.hostname doesn't include port information so grab that from
  // `X-Forwarded-Host` or `Host`
  let [, hostnamePortStr] = ((_req$get = req.get("X-Forwarded-Host")) === null || _req$get === void 0 ? void 0 : _req$get.split(":")) ?? [];
  let [, hostPortStr] = ((_req$get2 = req.get("host")) === null || _req$get2 === void 0 ? void 0 : _req$get2.split(":")) ?? [];
  let hostnamePort = Number.parseInt(hostnamePortStr, 10);
  let hostPort = Number.parseInt(hostPortStr, 10);
  let port = Number.isSafeInteger(hostnamePort) ? hostnamePort : Number.isSafeInteger(hostPort) ? hostPort : "";
  // Use req.hostname here as it respects the "trust proxy" setting
  let resolvedHost = `${req.hostname}${port ? `:${port}` : ""}`;
  // Use `req.originalUrl` so Remix is aware of the full path
  let url = new URL(`${req.protocol}://${resolvedHost}${req.originalUrl}`);
  let controller = new AbortController();
  let init = {
    method: req.method,
    headers: createRemixHeaders(req.headers),
    signal: controller.signal
  };
  if (req.method !== "GET" && req.method !== "HEAD") {
    init.body = node.createReadableStreamFromReadable(req);
    init.duplex = "half";
  }

  // Abort action/loaders once we can no longer write a response iff we have
  // not yet sent a response (i.e., `close` without `finish`)
  // `finish` -> done rendering the response
  // `close` -> response can no longer be written to
  res.on("finish", () => controller = null);
  res.on("close", () => {
    var _controller;
    return (_controller = controller) === null || _controller === void 0 ? void 0 : _controller.abort();
  });
  return new Request(url.href, init);
}
async function sendRemixResponse(res, nodeResponse) {
  var _nodeResponse$headers;
  res.statusMessage = nodeResponse.statusText;
  res.status(nodeResponse.status);
  for (let [key, value] of nodeResponse.headers.entries()) {
    res.append(key, value);
  }
  if ((_nodeResponse$headers = nodeResponse.headers.get("Content-Type")) !== null && _nodeResponse$headers !== void 0 && _nodeResponse$headers.match(/text\/event-stream/i)) {
    res.flushHeaders();
  }
  if (nodeResponse.body) {
    await node.writeReadableStreamToWritable(nodeResponse.body, res);
  } else {
    res.end();
  }
}

exports.createRemixHeaders = createRemixHeaders;
exports.createRemixRequest = createRemixRequest;
exports.createRequestHandler = createRequestHandler;
exports.sendRemixResponse = sendRemixResponse;
