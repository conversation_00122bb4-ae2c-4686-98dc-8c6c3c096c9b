/// <reference lib="dom.iterable" />
import type * as express from "express";
import type { AppLoadContext, ServerBuild } from "@remix-run/node";
/**
 * A function that returns the value to use as `context` in route `loader` and
 * `action` functions.
 *
 * You can think of this as an escape hatch that allows you to pass
 * environment/platform-specific values through to your loader/action, such as
 * values that are generated by Express middleware like `req.session`.
 */
export type GetLoadContextFunction = (req: express.Request, res: express.Response) => Promise<AppLoadContext> | AppLoadContext;
export type RequestHandler = (req: express.Request, res: express.Response, next: express.NextFunction) => Promise<void>;
/**
 * Returns a request handler for Express that serves the response using Remix.
 */
export declare function createRequestHandler({ build, getLoadContext, mode, }: {
    build: ServerBuild | (() => Promise<ServerBuild>);
    getLoadContext?: GetLoadContextFunction;
    mode?: string;
}): RequestHandler;
export declare function createRemixHeaders(requestHeaders: express.Request["headers"]): Headers;
export declare function createRemixRequest(req: express.Request, res: express.Response): Request;
export declare function sendRemixResponse(res: express.Response, nodeResponse: Response): Promise<void>;
