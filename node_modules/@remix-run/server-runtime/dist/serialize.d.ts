import type { Jsonify } from "./jsonify";
import type { TypedDeferredData, TypedResponse } from "./responses";
import type { ClientActionFunctionArgs, ClientLoaderFunctionArgs } from "./routeModules";
import { type SerializeFrom as SingleFetch_SerializeFrom } from "./single-fetch";
import type { Future } from "./future";
type SingleFetchEnabled = Future extends {
    v3_singleFetch: infer T extends boolean;
} ? T : false;
/**
 * Infer JSON serialized data type returned by a loader or action, while
 * avoiding deserialization if the input type if it's a clientLoader or
 * clientAction that returns a non-Response
 *
 * For example:
 * `type LoaderData = SerializeFrom<typeof loader>`
 *
 * @deprecated SerializeFrom is deprecated and will be removed in React Router
 * v7. Please use the generics on `useLoaderData`/etc. instead of manually
 * deserializing in Remix v2.  You can convert to the generated types once you
 * migrate to React Router v7.
 */
export type SerializeFrom<T> = SingleFetchEnabled extends true ? SingleFetch_SerializeFrom<T> : T extends (...args: any[]) => infer Output ? Parameters<T> extends [ClientLoaderFunctionArgs | ClientActionFunctionArgs] ? SerializeClient<Awaited<Output>> : Serialize<Awaited<Output>> : Jsonify<Awaited<T>>;
type SerializeClient<Output> = Output extends TypedDeferredData<infer U> ? {
    [K in keyof U as K extends symbol ? never : Promise<any> extends U[K] ? K : never]: DeferValueClient<U[K]>;
} & {
    [K in keyof U as Promise<any> extends U[K] ? never : K]: U[K];
} : Output extends TypedResponse<infer U> ? Jsonify<U> : Awaited<Output>;
type DeferValueClient<T> = T extends undefined ? undefined : T extends Promise<unknown> ? Promise<Awaited<T>> : T;
type Serialize<Output> = Output extends TypedDeferredData<infer U> ? {
    [K in keyof U as K extends symbol ? never : Promise<any> extends U[K] ? K : never]: DeferValue<U[K]>;
} & Jsonify<{
    [K in keyof U as Promise<any> extends U[K] ? never : K]: U[K];
}> : Output extends TypedResponse<infer U> ? Jsonify<U> : Jsonify<Output>;
type DeferValue<T> = T extends undefined ? undefined : T extends Promise<unknown> ? Promise<Jsonify<Awaited<T>>> : Jsonify<T>;
export {};
