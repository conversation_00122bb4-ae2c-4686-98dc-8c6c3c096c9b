/**
 * @remix-run/dev v2.16.7
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var isInRemixMonorepo = require('./is-in-remix-monorepo.js');

const ssrExternals = isInRemixMonorepo.isInRemixMonorepo() ? [
// This is only needed within the Remix repo because these
// packages are linked to a directory outside of node_modules
// so Vite treats them as internal code by default.
"@remix-run/architect", "@remix-run/cloudflare-pages", "@remix-run/cloudflare-workers", "@remix-run/cloudflare", "@remix-run/css-bundle", "@remix-run/deno", "@remix-run/dev", "@remix-run/express", "@remix-run/netlify", "@remix-run/node", "@remix-run/react", "@remix-run/serve", "@remix-run/server-runtime"] : undefined;

exports.ssrExternals = ssrExternals;
