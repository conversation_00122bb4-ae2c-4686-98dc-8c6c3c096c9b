/**
 * @remix-run/dev v2.16.7
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var server = require('vite-node/server');
var client = require('vite-node/client');
var sourceMap = require('vite-node/source-map');
var vite = require('./vite.js');
var ssrExternals = require('./ssr-externals.js');

async function createContext({
  root,
  mode
}) {
  await vite.preloadVite();
  let vite$1 = vite.getVite();
  let devServer = await vite$1.createServer({
    root,
    mode,
    server: {
      preTransformRequests: false,
      hmr: false,
      watch: null
    },
    ssr: {
      external: ssrExternals.ssrExternals
    },
    optimizeDeps: {
      noDiscovery: true
    },
    configFile: false,
    envFile: false,
    plugins: []
  });
  await devServer.pluginContainer.buildStart({});
  let server$1 = new server.ViteNodeServer(devServer);
  sourceMap.installSourcemapsSupport({
    getSourceMap: source => server$1.getSourceMap(source)
  });
  let runner = new client.ViteNodeRunner({
    root: devServer.config.root,
    base: devServer.config.base,
    fetchModule(id) {
      return server$1.fetchModule(id);
    },
    resolveId(id, importer) {
      return server$1.resolveId(id, importer);
    }
  });
  return {
    devServer,
    server: server$1,
    runner
  };
}

exports.createContext = createContext;
