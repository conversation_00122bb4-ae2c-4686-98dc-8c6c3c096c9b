/**
 * @remix-run/dev v2.16.7
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var path = require('pathe');
var invariant = require('../invariant.js');
var isInRemixMonorepo = require('./is-in-remix-monorepo.js');

function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

var path__default = /*#__PURE__*/_interopDefaultLegacy(path);

// eslint-disable-next-line @typescript-eslint/consistent-type-imports

let vite;
const viteImportSpecifier = isInRemixMonorepo.isInRemixMonorepo() ?
// Support testing against different versions of Vite by ensuring that Vite
// is resolved from the current working directory when running within this
// repo. If we don't do this, Vite will always be imported relative to this
// file, which means that it will always resolve to Vite 6.
`file:///${path__default["default"].normalize(require.resolve("vite/package.json", {
  paths: [process.cwd()]
})).replace("package.json", "dist/node/index.js")}` : "vite";
async function preloadVite() {
  // Use a dynamic import to force Vite to use the ESM build. If we don't do
  // this, Vite logs CJS deprecation warnings.
  vite = await import(viteImportSpecifier);
}
function getVite() {
  invariant["default"](vite, "getVite() called before preloadVite()");
  return vite;
}

exports.getVite = getVite;
exports.preloadVite = preloadVite;
