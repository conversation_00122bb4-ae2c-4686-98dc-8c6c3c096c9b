/**
 * @remix-run/dev v2.16.7
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var path = require('node:path');

function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

var path__default = /*#__PURE__*/_interopDefaultLegacy(path);

function isInRemixMonorepo() {
  try {
    let devPath = path__default["default"].dirname(require.resolve("@remix-run/node/package.json"));
    let devParentDir = path__default["default"].basename(path__default["default"].resolve(devPath, ".."));
    return devParentDir === "packages";
  } catch {
    return false;
  }
}

exports.isInRemixMonorepo = isInRemixMonorepo;
