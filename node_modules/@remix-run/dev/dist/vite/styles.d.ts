import { type ServerBuild } from "@remix-run/server-runtime";
import { type ViteDevServer } from "vite";
import { type RemixConfig as ResolvedRemixConfig } from "../config";
export declare const isCssModulesFile: (file: string) => boolean;
export declare const isCssUrlWithoutSideEffects: (url: string) => boolean;
export declare const getStylesForUrl: ({ viteDevServer, rootDirectory, remixConfig, entryClientFilePath, cssModulesManifest, build, url, }: {
    viteDevServer: ViteDevServer;
    rootDirectory: string;
    remixConfig: Pick<ResolvedRemixConfig, "appDirectory" | "routes">;
    entryClientFilePath: string;
    cssModulesManifest: Record<string, string>;
    build: ServerBuild;
    url: string | undefined;
}) => Promise<string | undefined>;
