{"name": "@remix-run/dev", "version": "2.16.7", "description": "Dev tools and CLI for Remix", "homepage": "https://remix.run", "bugs": {"url": "https://github.com/remix-run/remix/issues"}, "repository": {"type": "git", "url": "https://github.com/remix-run/remix", "directory": "packages/remix-dev"}, "license": "MIT", "main": "dist/index.js", "typings": "dist/index.d.ts", "bin": {"remix": "dist/cli.js"}, "scripts": {"tsc": "tsc"}, "dependencies": {"@babel/core": "^7.21.8", "@babel/generator": "^7.21.5", "@babel/parser": "^7.21.8", "@babel/plugin-syntax-decorators": "^7.22.10", "@babel/plugin-syntax-jsx": "^7.21.4", "@babel/preset-typescript": "^7.21.5", "@babel/traverse": "^7.23.2", "@babel/types": "^7.22.5", "@mdx-js/mdx": "^2.3.0", "@npmcli/package-json": "^4.0.1", "@remix-run/node": "workspace:*", "@remix-run/router": "1.23.0", "@remix-run/server-runtime": "workspace:*", "@types/mdx": "^2.0.5", "@vanilla-extract/integration": "^6.2.0", "arg": "^5.0.1", "cacache": "^17.1.3", "chalk": "^4.1.2", "chokidar": "^3.5.1", "cross-spawn": "^7.0.3", "dotenv": "^16.0.0", "es-module-lexer": "^1.3.1", "esbuild": "0.17.6", "esbuild-plugins-node-modules-polyfill": "^1.6.0", "execa": "5.1.1", "exit-hook": "2.2.1", "express": "^4.20.0", "fs-extra": "^10.0.0", "get-port": "^5.1.1", "gunzip-maybe": "^1.4.2", "jsesc": "3.0.2", "json5": "^2.2.2", "lodash": "^4.17.21", "lodash.debounce": "^4.0.8", "minimatch": "^9.0.0", "ora": "^5.4.1", "pathe": "^1.1.2", "picocolors": "^1.0.0", "picomatch": "^2.3.1", "pidtree": "^0.6.0", "postcss": "^8.4.19", "postcss-discard-duplicates": "^5.1.0", "postcss-load-config": "^4.0.1", "postcss-modules": "^6.0.0", "prettier": "^2.7.1", "pretty-ms": "^7.0.1", "react-refresh": "^0.14.0", "remark-frontmatter": "4.0.1", "remark-mdx-frontmatter": "^1.0.1", "semver": "^7.3.7", "set-cookie-parser": "^2.6.0", "tar-fs": "^2.1.1", "tsconfig-paths": "^4.0.0", "valibot": "^0.41.0", "vite-node": "^3.1.3", "ws": "^7.5.10"}, "devDependencies": {"@remix-run/cloudflare": "workspace:*", "@remix-run/deno": "workspace:*", "@remix-run/react": "workspace:*", "@remix-run/serve": "workspace:*", "@remix-run/testing": "workspace:*", "@types/babel__core": "^7.20.5", "@types/babel__generator": "^7.6.8", "@types/babel__traverse": "^7.20.5", "@types/cacache": "^17.0.0", "@types/cross-spawn": "^6.0.2", "@types/express": "^4.17.9", "@types/gunzip-maybe": "^1.4.0", "@types/jsesc": "^3.0.1", "@types/lodash.debounce": "^4.0.6", "@types/node": "^18.17.1", "@types/npmcli__package-json": "^4.0.0", "@types/picomatch": "^2.3.0", "@types/prettier": "^2.7.3", "@types/set-cookie-parser": "^2.4.1", "@types/shelljs": "^0.8.11", "@types/tar-fs": "^2.0.1", "@types/ws": "^7.4.1", "esbuild-register": "^3.3.2", "fast-glob": "3.2.11", "msw": "^1.2.3", "strip-ansi": "^6.0.1", "tiny-invariant": "^1.2.0", "wrangler": "^3.28.2"}, "peerDependencies": {"@remix-run/react": "^2.16.7", "@remix-run/serve": "^2.16.7", "typescript": "^5.1.0", "vite": "^5.1.0 || ^6.0.0", "wrangler": "^3.28.2"}, "peerDependenciesMeta": {"@remix-run/serve": {"optional": true}, "typescript": {"optional": true}, "vite": {"optional": true}, "wrangler": {"optional": true}}, "engines": {"node": ">=18.0.0"}, "files": ["dist/", "compiler/shims/", "server-build.js", "server-build.d.ts", "CHANGELOG.md", "LICENSE.md", "README.md"]}