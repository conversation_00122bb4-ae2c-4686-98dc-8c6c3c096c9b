/**
 * @remix-run/dev v2.16.7
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var colors = require('picocolors');

function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

var colors__default = /*#__PURE__*/_interopDefaultLegacy(colors);

let format = ({
  label,
  color
}) => (message, details = []) => {
  let lines = [];
  lines.push((colors__default["default"].isColorSupported ? colors__default["default"].inverse(color(` ${label} `)) : `[${label}]`) + " " + message);
  if (details.length > 0) {
    for (let detail of details) {
      lines.push(color("┃") + " " + colors__default["default"].gray(detail));
    }
    lines.push(color("┗"));
  }
  return lines.join("\n");
};

exports.format = format;
