/**
 * @remix-run/dev v2.16.7
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var index = require('./cli/index.js');
var routes = require('./config/routes.js');
var flatRoutes = require('./config/flat-routes.js');
var dependencies = require('./dependencies.js');
var index$1 = require('./vite/index.js');
var cloudflareProxyPlugin = require('./vite/cloudflare-proxy-plugin.js');



exports.cli = index;
exports.UNSAFE_defineRoutes = routes.defineRoutes;
exports.UNSAFE_getRouteConfigAppDirectory = routes.getRouteConfigAppDirectory;
exports.UNSAFE_routeManifestToRouteConfig = routes.routeManifestToRouteConfig;
exports.UNSAFE_flatRoutes = flatRoutes.flatRoutes;
exports.getDependenciesToBundle = dependencies.getDependenciesToBundle;
exports.vitePlugin = index$1.vitePlugin;
exports.cloudflareDevProxyVitePlugin = cloudflareProxyPlugin.cloudflareDevProxyVitePlugin;
