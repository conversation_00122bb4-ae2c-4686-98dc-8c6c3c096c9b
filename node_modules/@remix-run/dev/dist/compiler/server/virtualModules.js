/**
 * @remix-run/dev v2.16.7
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

const serverBuildVirtualModule = {
  id: "@remix-run/dev/server-build",
  filter: /^@remix-run\/dev\/server-build$/
};
const assetsManifestVirtualModule = {
  id: "@remix-run/dev/assets-manifest",
  filter: /^@remix-run\/dev\/assets-manifest$/
};

exports.assetsManifestVirtualModule = assetsManifestVirtualModule;
exports.serverBuildVirtualModule = serverBuildVirtualModule;
