#!/usr/bin/env node
/**
 * @remix-run/dev v2.16.7
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

throw new Error("@remix-run/dev/server-build is not meant to be used directly from node_modules." + " It exists to provide type definitions for a virtual module provided" + " by the Remix compiler at build time.");
const mode = undefined;
const assets = undefined;
const basename = undefined;
const entry = undefined;
const routes = undefined;
const future = undefined;
const publicPath = undefined;
// prettier-ignore
const assetsBuildDirectory = undefined;
const isSpaMode = undefined;

exports.assets = assets;
exports.assetsBuildDirectory = assetsBuildDirectory;
exports.basename = basename;
exports.entry = entry;
exports.future = future;
exports.isSpaMode = isSpaMode;
exports.mode = mode;
exports.publicPath = publicPath;
exports.routes = routes;
