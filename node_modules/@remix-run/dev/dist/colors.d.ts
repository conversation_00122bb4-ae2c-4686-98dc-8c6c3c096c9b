import chalk from "chalk";
export declare const heading: chalk.Chalk | (<T>(x: T) => T);
export declare const arg: chalk.Chalk | (<T>(x: T) => T);
export declare const error: chalk.Chalk | (<T>(x: T) => T);
export declare const warning: chalk.Chalk | (<T>(x: T) => T);
export declare const hint: chalk.Chalk | (<T>(x: T) => T);
export declare const logoBlue: chalk.Chalk | (<T>(x: T) => T);
export declare const logoGreen: chalk.Chalk | (<T>(x: T) => T);
export declare const logoYellow: chalk.Chalk | (<T>(x: T) => T);
export declare const logoPink: chalk.Chalk | (<T>(x: T) => T);
export declare const logoRed: chalk.Chalk | (<T>(x: T) => T);
export declare const bold: chalk.Chalk | (<T>(x: T) => T);
export declare const blue: chalk.Chalk | (<T>(x: T) => T);
export declare const cyan: chalk.Chalk | (<T>(x: T) => T);
export declare const gray: chalk.Chalk | (<T>(x: T) => T);
export declare const red: chalk.Chalk | (<T>(x: T) => T);
export declare const yellow: chalk.Chalk | (<T>(x: T) => T);
