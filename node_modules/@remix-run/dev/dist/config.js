/**
 * @remix-run/dev v2.16.7
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */
'use strict';

Object.defineProperty(exports, '__esModule', { value: true });

var node_child_process = require('node:child_process');
var path = require('node:path');
var url = require('node:url');
var colors = require('picocolors');
var fse = require('fs-extra');
var PackageJson = require('@npmcli/package-json');
var routes = require('./config/routes.js');
var serverModes = require('./config/serverModes.js');
var virtualModules = require('./compiler/server/virtualModules.js');
var flatRoutes = require('./config/flat-routes.js');
var detectPackageManager = require('./cli/detectPackageManager.js');
var logger = require('./tux/logger.js');
var invariant = require('./invariant.js');

function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }

var path__default = /*#__PURE__*/_interopDefaultLegacy(path);
var colors__default = /*#__PURE__*/_interopDefaultLegacy(colors);
var fse__default = /*#__PURE__*/_interopDefaultLegacy(fse);
var PackageJson__default = /*#__PURE__*/_interopDefaultLegacy(PackageJson);

/**
 * The user-provided config in `remix.config.js`.
 */

/**
 * Fully resolved configuration object we use throughout Remix.
 */

/**
 * Returns a fully resolved config object from the remix.config.js in the given
 * root directory.
 */
async function readConfig(remixRoot, serverMode) {
  if (!remixRoot) {
    remixRoot = process.env.REMIX_ROOT || process.cwd();
  }
  let rootDirectory = path__default["default"].resolve(remixRoot);
  let configFile = findConfig(rootDirectory, "remix.config", configExts);
  let appConfig = {};
  if (configFile) {
    let appConfigModule;
    try {
      var _appConfigModule;
      // shout out to next
      // https://github.com/vercel/next.js/blob/b15a976e11bf1dc867c241a4c1734757427d609c/packages/next/server/config.ts#L748-L765
      if (process.env.JEST_WORKER_ID) {
        // dynamic import does not currently work inside vm which
        // jest relies on, so we fall back to require for this case
        // https://github.com/nodejs/node/issues/35889
        appConfigModule = require(configFile);
      } else {
        let stat = fse__default["default"].statSync(configFile);
        appConfigModule = await import(url.pathToFileURL(configFile).href + "?t=" + stat.mtimeMs);
      }
      appConfig = ((_appConfigModule = appConfigModule) === null || _appConfigModule === void 0 ? void 0 : _appConfigModule.default) || appConfigModule;
    } catch (error) {
      throw new Error(`Error loading Remix config at ${configFile}\n${String(error)}`);
    }
  }
  return await resolveConfig(appConfig, {
    rootDirectory,
    serverMode
  });
}
let isFirstLoad = true;
let lastValidRoutes = {};
async function resolveConfig(appConfig, {
  rootDirectory,
  serverMode = serverModes.ServerMode.Production,
  isSpaMode = false,
  routeConfigChanged = false,
  vite,
  viteUserConfig,
  routesViteNodeContext
}) {
  var _appConfig$future, _appConfig$future2, _appConfig$future3, _appConfig$future4, _appConfig$future5, _appConfig$future6, _appConfig$future7, _appConfig$future8, _appConfig$future9;
  if (!serverModes.isValidServerMode(serverMode)) {
    throw new Error(`Invalid server mode "${serverMode}"`);
  }
  let serverBuildPath = path__default["default"].resolve(rootDirectory, appConfig.serverBuildPath ?? "build/index.js");
  let serverBuildTargetEntryModule = `export * from ${JSON.stringify(virtualModules.serverBuildVirtualModule.id)};`;
  let serverConditions = appConfig.serverConditions;
  let serverDependenciesToBundle = appConfig.serverDependenciesToBundle || [];
  let serverEntryPoint = appConfig.server;
  let serverMainFields = appConfig.serverMainFields;
  let serverMinify = appConfig.serverMinify;
  let serverModuleFormat = appConfig.serverModuleFormat || "esm";
  let serverPlatform = appConfig.serverPlatform || "node";
  serverMainFields ??= serverModuleFormat === "esm" ? ["module", "main"] : ["main", "module"];
  serverMinify ??= false;
  let serverNodeBuiltinsPolyfill = appConfig.serverNodeBuiltinsPolyfill;
  let browserNodeBuiltinsPolyfill = appConfig.browserNodeBuiltinsPolyfill;
  let mdx = appConfig.mdx;
  let postcss = appConfig.postcss ?? true;
  let tailwind = appConfig.tailwind ?? true;
  let appDirectory = path__default["default"].resolve(rootDirectory, appConfig.appDirectory || "app");
  let cacheDirectory = path__default["default"].resolve(rootDirectory, appConfig.cacheDirectory || ".cache");
  let defaultsDirectory = path__default["default"].resolve(__dirname, "config", "defaults");
  let userEntryClientFile = findEntry(appDirectory, "entry.client");
  let userEntryServerFile = findEntry(appDirectory, "entry.server");
  let entryServerFile;
  let entryClientFile = userEntryClientFile || "entry.client.tsx";
  let pkgJson = await PackageJson__default["default"].load(rootDirectory);
  let deps = pkgJson.content.dependencies ?? {};
  if (isSpaMode && ((_appConfig$future = appConfig.future) === null || _appConfig$future === void 0 ? void 0 : _appConfig$future.v3_singleFetch) != true) {
    // This is a super-simple default since we don't need streaming in SPA Mode.
    // We can include this in a remix-spa template, but right now `npx remix reveal`
    // will still expose the streaming template since that command doesn't have
    // access to the `ssr:false` flag in the vite config (the streaming template
    // works just fine so maybe instea dof having this we _only have this version
    // in the template...).  We let users manage an entry.server file in SPA Mode
    // so they can de ide if they want to hydrate the full document or just an
    // embedded `<div id="app">` or whatever.
    entryServerFile = "entry.server.spa.tsx";
  } else if (userEntryServerFile) {
    entryServerFile = userEntryServerFile;
  } else {
    let serverRuntime = deps["@remix-run/deno"] ? "deno" : deps["@remix-run/cloudflare"] ? "cloudflare" : deps["@remix-run/node"] ? "node" : undefined;
    if (!serverRuntime) {
      let serverRuntimes = ["@remix-run/deno", "@remix-run/cloudflare", "@remix-run/node"];
      let formattedList = disjunctionListFormat.format(serverRuntimes);
      throw new Error(`Could not determine server runtime. Please install one of the following: ${formattedList}`);
    }
    if (!deps["isbot"]) {
      console.log("adding `isbot` to your package.json, you should commit this change");
      pkgJson.update({
        dependencies: {
          ...pkgJson.content.dependencies,
          isbot: "^4"
        }
      });
      await pkgJson.save();
      let packageManager = detectPackageManager.detectPackageManager() ?? "npm";
      node_child_process.execSync(`${packageManager} install`, {
        cwd: rootDirectory,
        stdio: "inherit"
      });
    }
    entryServerFile = `entry.server.${serverRuntime}.tsx`;
  }
  let entryClientFilePath = userEntryClientFile ? path__default["default"].resolve(appDirectory, userEntryClientFile) : path__default["default"].resolve(defaultsDirectory, entryClientFile);
  let entryServerFilePath = userEntryServerFile ? path__default["default"].resolve(appDirectory, userEntryServerFile) : path__default["default"].resolve(defaultsDirectory, entryServerFile);
  let assetsBuildDirectory = appConfig.assetsBuildDirectory || path__default["default"].join("public", "build");
  let absoluteAssetsBuildDirectory = path__default["default"].resolve(rootDirectory, assetsBuildDirectory);
  let publicPath = addTrailingSlash(appConfig.publicPath || "/build/");
  let rootRouteFile = findEntry(appDirectory, "root");
  if (!rootRouteFile) {
    throw new Error(`Missing "root" route file in ${appDirectory}`);
  }
  let routes$1 = {
    root: {
      path: "",
      id: "root",
      file: rootRouteFile
    }
  };
  if ((_appConfig$future2 = appConfig.future) !== null && _appConfig$future2 !== void 0 && _appConfig$future2.v3_routeConfig) {
    invariant["default"](routesViteNodeContext);
    invariant["default"](vite);
    let routeConfigFile = findEntry(appDirectory, "routes");
    class FriendlyError extends Error {}
    let logger = vite.createLogger(viteUserConfig === null || viteUserConfig === void 0 ? void 0 : viteUserConfig.logLevel, {
      prefix: "[remix]"
    });
    try {
      if (appConfig.routes) {
        throw new FriendlyError('The "routes" config option is not supported when a "routes.ts" file is present. You should migrate these routes into "routes.ts".');
      }
      if (!routeConfigFile) {
        let routeConfigDisplayPath = vite.normalizePath(path__default["default"].relative(rootDirectory, path__default["default"].join(appDirectory, "routes.ts")));
        throw new FriendlyError(`Route config file not found at "${routeConfigDisplayPath}".`);
      }
      routes.setRouteConfigAppDirectory(appDirectory);
      let routeConfigExport = (await routesViteNodeContext.runner.executeFile(path__default["default"].join(appDirectory, routeConfigFile))).default;
      let routeConfig = await routeConfigExport;
      let result = routes.validateRouteConfig({
        routeConfigFile,
        routeConfig
      });
      if (!result.valid) {
        throw new FriendlyError(result.message);
      }
      routes$1 = {
        ...routes$1,
        ...routes.configRoutesToRouteManifest(routeConfig)
      };
      lastValidRoutes = routes$1;
      if (routeConfigChanged) {
        logger.info(colors__default["default"].green("Route config changed."), {
          clear: true,
          timestamp: true
        });
      }
    } catch (error) {
      var _error$loc, _error$loc2, _error$frame$trim, _error$frame;
      logger.error(error instanceof FriendlyError ? colors__default["default"].red(error.message) : [colors__default["default"].red(`Route config in "${routeConfigFile}" is invalid.`), "", (_error$loc = error.loc) !== null && _error$loc !== void 0 && _error$loc.file && (_error$loc2 = error.loc) !== null && _error$loc2 !== void 0 && _error$loc2.column && error.frame ? [path__default["default"].relative(appDirectory, error.loc.file) + ":" + error.loc.line + ":" + error.loc.column, (_error$frame$trim = (_error$frame = error.frame).trim) === null || _error$frame$trim === void 0 ? void 0 : _error$frame$trim.call(_error$frame)] : error.stack].flat().join("\n") + "\n", {
        error,
        clear: !isFirstLoad,
        timestamp: !isFirstLoad
      });

      // Bail if this is the first time loading config, otherwise keep the dev server running
      if (isFirstLoad) {
        process.exit(1);
      }

      // Keep dev server running with the last valid routes to allow for correction
      routes$1 = lastValidRoutes;
    }
  } else {
    if (fse__default["default"].existsSync(path__default["default"].resolve(appDirectory, "routes"))) {
      let fileRoutes = flatRoutes.flatRoutes(appDirectory, appConfig.ignoredRouteFiles);
      for (let route of Object.values(fileRoutes)) {
        routes$1[route.id] = {
          ...route,
          parentId: route.parentId || "root"
        };
      }
    }
  }
  if (appConfig.routes) {
    let manualRoutes = await appConfig.routes(routes.defineRoutes);
    for (let route of Object.values(manualRoutes)) {
      routes$1[route.id] = {
        ...route,
        parentId: route.parentId || "root"
      };
    }
  }
  let watchPaths = [];
  if (typeof appConfig.watchPaths === "function") {
    let directories = await appConfig.watchPaths();
    watchPaths = watchPaths.concat(Array.isArray(directories) ? directories : [directories]);
  } else if (appConfig.watchPaths) {
    watchPaths = watchPaths.concat(Array.isArray(appConfig.watchPaths) ? appConfig.watchPaths : [appConfig.watchPaths]);
  }

  // When tsconfigPath is undefined, the default "tsconfig.json" is not
  // found in the root directory.
  let tsconfigPath;
  let rootTsconfig = path__default["default"].resolve(rootDirectory, "tsconfig.json");
  let rootJsConfig = path__default["default"].resolve(rootDirectory, "jsconfig.json");
  if (fse__default["default"].existsSync(rootTsconfig)) {
    tsconfigPath = rootTsconfig;
  } else if (fse__default["default"].existsSync(rootJsConfig)) {
    tsconfigPath = rootJsConfig;
  }

  // Note: When a future flag is removed from here, it should be added to the
  // list below, so we can let folks know if they have obsolete flags in their
  // config.  If we ever convert remix.config.js to a TS file, so we get proper
  // typings this won't be necessary anymore.
  let future = {
    v3_fetcherPersist: ((_appConfig$future3 = appConfig.future) === null || _appConfig$future3 === void 0 ? void 0 : _appConfig$future3.v3_fetcherPersist) === true,
    v3_relativeSplatPath: ((_appConfig$future4 = appConfig.future) === null || _appConfig$future4 === void 0 ? void 0 : _appConfig$future4.v3_relativeSplatPath) === true,
    v3_throwAbortReason: ((_appConfig$future5 = appConfig.future) === null || _appConfig$future5 === void 0 ? void 0 : _appConfig$future5.v3_throwAbortReason) === true,
    v3_routeConfig: ((_appConfig$future6 = appConfig.future) === null || _appConfig$future6 === void 0 ? void 0 : _appConfig$future6.v3_routeConfig) === true,
    v3_singleFetch: ((_appConfig$future7 = appConfig.future) === null || _appConfig$future7 === void 0 ? void 0 : _appConfig$future7.v3_singleFetch) === true,
    v3_lazyRouteDiscovery: ((_appConfig$future8 = appConfig.future) === null || _appConfig$future8 === void 0 ? void 0 : _appConfig$future8.v3_lazyRouteDiscovery) === true,
    unstable_optimizeDeps: ((_appConfig$future9 = appConfig.future) === null || _appConfig$future9 === void 0 ? void 0 : _appConfig$future9.unstable_optimizeDeps) === true
  };
  if (appConfig.future) {
    let userFlags = appConfig.future;
    let deprecatedFlags = ["unstable_cssModules", "unstable_cssSideEffectImports", "unstable_dev", "unstable_postcss", "unstable_routeConfig", "unstable_tailwind", "unstable_vanillaExtract", "v2_errorBoundary", "v2_headers", "v2_meta", "v2_normalizeFormMethod", "v2_routeConvention"];
    if ("unstable_routeConfig" in userFlags) {
      logger.logger.warn("The `unstable_routeConfig` future flag has been stabilized as `v3_routeConfig`.");
    }
    if ("v2_dev" in userFlags) {
      if (userFlags.v2_dev === true) {
        deprecatedFlags.push("v2_dev");
      } else {
        logger.logger.warn("The `v2_dev` future flag is obsolete.", {
          details: ["Move your dev options from `future.v2_dev` to `dev` within your `remix.config.js` file"]
        });
      }
    }
    let obsoleteFlags = deprecatedFlags.filter(f => f in userFlags);
    if (obsoleteFlags.length > 0) {
      logger.logger.warn(`The following Remix future flags are now obsolete ` + `and can be removed from your remix.config.js file:\n` + obsoleteFlags.map(f => `- ${f}\n`).join(""));
    }
  }
  logFutureFlagWarnings(appConfig.future || {});
  isFirstLoad = false;
  return {
    appDirectory,
    cacheDirectory,
    entryClientFile,
    entryClientFilePath,
    entryServerFile,
    entryServerFilePath,
    dev: appConfig.dev ?? {},
    assetsBuildDirectory: absoluteAssetsBuildDirectory,
    relativeAssetsBuildDirectory: assetsBuildDirectory,
    publicPath,
    rootDirectory,
    routes: routes$1,
    serverBuildPath,
    serverBuildTargetEntryModule,
    serverConditions,
    serverDependenciesToBundle,
    serverEntryPoint,
    serverMainFields,
    serverMinify,
    serverMode,
    serverModuleFormat,
    serverNodeBuiltinsPolyfill,
    browserNodeBuiltinsPolyfill,
    serverPlatform,
    mdx,
    postcss,
    tailwind,
    watchPaths,
    tsconfigPath,
    future
  };
}
function addTrailingSlash(path) {
  return path.endsWith("/") ? path : path + "/";
}
const entryExts = [".js", ".jsx", ".ts", ".tsx"];
function findEntry(dir, basename) {
  for (let ext of entryExts) {
    let file = path__default["default"].resolve(dir, basename + ext);
    if (fse__default["default"].existsSync(file)) return path__default["default"].relative(dir, file);
  }
  return undefined;
}
const configExts = [".js", ".cjs", ".mjs"];
function findConfig(dir, basename, extensions) {
  for (let ext of extensions) {
    let name = basename + ext;
    let file = path__default["default"].join(dir, name);
    if (fse__default["default"].existsSync(file)) return file;
  }
  return undefined;
}

// adds types for `Intl.ListFormat` to the global namespace
// we could also update our `tsconfig.json` to include `lib: ["es2021"]`

let disjunctionListFormat = new Intl.ListFormat("en", {
  style: "long",
  type: "disjunction"
});
function logFutureFlagWarning(args) {
  logger.logger.warn(args.message, {
    key: args.flag,
    details: [`You can use the \`${args.flag}\` future flag to opt-in early.`, `-> https://remix.run/docs/en/2.13.1/start/future-flags#${args.flag}`]
  });
}
function logFutureFlagWarnings(future) {
  if (future.v3_fetcherPersist === undefined) {
    logFutureFlagWarning({
      flag: "v3_fetcherPersist",
      message: "Fetcher persistence behavior is changing in React Router v7"
    });
  }
  if (future.v3_lazyRouteDiscovery === undefined) {
    logFutureFlagWarning({
      flag: "v3_lazyRouteDiscovery",
      message: "Route discovery/manifest behavior is changing in React Router v7"
    });
  }
  if (future.v3_relativeSplatPath === undefined) {
    logFutureFlagWarning({
      flag: "v3_relativeSplatPath",
      message: "Relative routing behavior for splat routes is changing in React Router v7"
    });
  }
  if (future.v3_singleFetch === undefined) {
    logFutureFlagWarning({
      flag: "v3_singleFetch",
      message: "Data fetching is changing to a single fetch in React Router v7"
    });
  }
  if (future.v3_throwAbortReason === undefined) {
    logFutureFlagWarning({
      flag: "v3_throwAbortReason",
      message: "The format of errors thrown on aborted requests is changing in React Router v7"
    });
  }
}

exports.findConfig = findConfig;
exports.logFutureFlagWarnings = logFutureFlagWarnings;
exports.readConfig = readConfig;
exports.resolveConfig = resolveConfig;
