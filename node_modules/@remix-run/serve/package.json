{"name": "@remix-run/serve", "version": "2.16.7", "description": "Production application server for Remix", "bugs": {"url": "https://github.com/remix-run/remix/issues"}, "repository": {"type": "git", "url": "https://github.com/remix-run/remix", "directory": "packages/remix-serve"}, "license": "MIT", "bin": {"remix-serve": "dist/cli.js"}, "dependencies": {"chokidar": "^3.5.3", "compression": "^1.7.4", "express": "^4.20.0", "get-port": "5.1.1", "morgan": "^1.10.0", "source-map-support": "^0.5.21", "@remix-run/express": "2.16.7", "@remix-run/node": "2.16.7"}, "devDependencies": {"@types/compression": "^1.7.0", "@types/express": "^4.17.9", "@types/morgan": "^1.9.2", "@types/source-map-support": "^0.5.6"}, "engines": {"node": ">=18.0.0"}, "files": ["dist/", "CHANGELOG.md", "LICENSE.md", "README.md"], "scripts": {"tsc": "tsc"}}